from raslogger import LoggingFactory

from app.utils.sorting.sort_patterns import *

logger = LoggingFactory.get_logger(__name__)


# Defines custom mappings for sorting patterns, if they differ from the default
sort_pattern_map = {
    "federal_statutes": FederalStatSortPattern(),
    "federal_common_law": FederalComLawSortPattern(),
    "federal_constitutional": FederalConstitSortPattern(),
    "california_statutes": FirstLineCitationBaseSortPattern(),
    "california_common_law": SimpleAlphaSortPattern(),
    "california_constitutional": FirstLineCitationBaseSortPattern(),
    "florida_statutes": FirstLineCitationBaseSortPattern(),
    "florida_common_law": SimpleAlphaSortPattern(),
    "florida_constitutional": FirstLineCitationBaseSortPattern(),
    "georgia_statutes": FirstLineCitationBaseSortPattern(),
    "georgia_common_law": SimpleAlphaSortPattern(),
    "georgia_constitutional": FirstLineCitationBaseSortPattern(),
    "illinois_statutes": FirstLineCitationBaseSortPattern(),
    "illinois_common_law": SimpleAlphaSortPattern(),
    "illinois_constitutional": FirstLineCitationBaseSortPattern(),
    "maryland_statutes": FirstLineCitationBaseSortPattern(),
    "maryland_common_law": SimpleAlphaSortPattern(),
    "maryland_constitutional": FirstLineCitationBaseSortPattern(),
    "massachusetts_statutes": FirstLineCitationBaseSortPattern(),
    "massachusetts_common_law": SimpleAlphaSortPattern(),
    "massachusetts_constitutional": FirstLineCitationBaseSortPattern(),
    "michigan_statutes": FirstLineCitationBaseSortPattern(),
    "michigan_common_law": SimpleAlphaSortPattern(),
    "michigan_constitutional": FirstLineCitationBaseSortPattern(),
    "north_carolina_statutes": FirstLineCitationBaseSortPattern(),
    "north_carolina_common_law": SimpleAlphaSortPattern(),
    "north_carolina_constitutional": FirstLineCitationBaseSortPattern(),
    "new_jersey_statutes": FirstLineCitationBaseSortPattern(),
    "new_jersey_common_law": SimpleAlphaSortPattern(),
    "new_jersey_constitutional": FirstLineCitationBaseSortPattern(),
    "new_york_statutes": FirstLineCitationBaseSortPattern(),
    "new_york_common_law": SimpleAlphaSortPattern(),
    "new_york_constitutional": FirstLineCitationBaseSortPattern(),
    "ohio_statutes": FirstLineCitationBaseSortPattern(),
    "ohio_common_law": SimpleAlphaSortPattern(),
    "ohio_constitutional": FirstLineCitationBaseSortPattern(),
    "pennsylvania_statutes": FirstLineCitationBaseSortPattern(),
    "pennsylvania_common_law": SimpleAlphaSortPattern(),
    "pennsylvania_constitutional": FirstLineCitationBaseSortPattern(),
    "texas_statutes": FirstLineCitationBaseSortPattern(),
    "texas_common_law": SimpleAlphaSortPattern(),
    "texas_constitutional": FirstLineCitationBaseSortPattern(),
}

# Defines the ordering of the jurisdictions
juris_group_map = {
    "federal": 0,
    "california": 1,
    "florida": 2,
    "georgia": 3,
    "illinois": 4,
    'maryland': 5,
    "massachusetts": 6,
    "michigan": 7,
    "new_jersey": 8,
    "new_york": 9,
    "north_carolina": 10,
    "ohio": 11,
    "pennsylvania": 12,
    "texas": 13,
}


def get_sort_pattern(jurisdiction: str, source_law: str) -> BaseSortPattern:
    map_key = jurisdiction + "_" + source_law
    if map_key in sort_pattern_map:
        return sort_pattern_map[map_key]
    else:
        return BaseSortPattern()


def do_sort(source_law: str, entries: list[dict], is_mock_run: bool) -> list[dict]:
    if not is_mock_run:
        sortable_chunks: list[SortableChunk] = prepare_sortable_chunks(entries, source_law)

        sort_key_grouped_chunks: dict[str, list[SortableChunk]] = {}

        for chunk in sortable_chunks:
            if chunk.primary_sort_key in sort_key_grouped_chunks:
                sort_key_grouped_chunks[chunk.primary_sort_key].append(chunk)
            else:
                sort_key_grouped_chunks[chunk.primary_sort_key] = [chunk]

        yes_list: list[SortableChunk] = []
        mixed_list: list[SortableChunk] = []
        possibly_list: list[SortableChunk] = []

        for chunks in sort_key_grouped_chunks.values():
            answers = [chunk.result_quality for chunk in chunks]
            if all(answer == 0 for answer in answers):
                yes_list += chunks
            elif all(answer == 1 for answer in answers):
                possibly_list += chunks
            else:
                mixed_list += chunks

        # sort "yes" list, "mixed" list, and "possibly" list separately, and then concatenate them all together
        sorted_result: list[dict] = []
        sorted_result += sort_entries(yes_list)
        sorted_result += sort_entries(mixed_list)
        sorted_result += sort_entries(possibly_list)

    # LLM Proxy Mocks don't match jurisdiction or claim type on each request, so some sort patterns will throw an
    # exception when a mock result with an unexpected pattern is returned, so we need to bypass this functionality when
    # doing a mock run and leave the sorting pattern as it was passed in
    else:
        sorted_result = entries

    # sub_sort_order has served its purpose, and we don't want to include it in the output
    for entry in sorted_result:
        if 'sub_sort_order' in entry:
            del entry['sub_sort_order']

    return sorted_result


def prepare_sortable_chunks(entries: list[dict], source_law: str) -> list[SortableChunk]:
    sortable_chunks: list[SortableChunk] = []
    for entry in entries:
        jurisdictions: list[str] = entry['jurisdictions']
        result_quality: int = map_to_quality_score(entry['answer'])
        for jurisdiction in jurisdictions:
            formatted_jurisdiction = jurisdiction.lower().replace(" ", "_")
            formatted_source_law = source_law.lower()
            sort_pattern = get_sort_pattern(jurisdiction=formatted_jurisdiction, source_law=formatted_source_law)
            juris_group = juris_group_map[formatted_jurisdiction]
            primary_sort_str = entry[sort_pattern.SORT_KEY_FIELD]
            try:
                sub_sort_order: int = int(entry[sort_pattern.SUB_ORDER_SORT_FIELD])
            except Exception as e:
                logger.warning(f"SortingUtil:: {type(e).__name__} exception occurred {e}; using default value of 1")
                sub_sort_order = 1

            sortable_chunk = SortableChunk(juris_group=juris_group,
                                           result_quality=result_quality,
                                           data_dict=entry,
                                           primary_sort_key=primary_sort_str,
                                           sub_sort_order=sub_sort_order)
            try:
                sort_pattern.populate_sort_values(sortable_chunk)
            except Exception as ex:
                logger.error(
                    f"sort_util::{type(ex).__name__} exception {ex} occurred while populating sort "
                    f"values; SortableChunk was {sortable_chunk}")
                raise ex

            sortable_chunks.append(sortable_chunk)
    return sortable_chunks


def map_to_quality_score(answer: str) -> int:
    if answer == "Yes":
        return 0
    elif answer == "Possibly with Additional Fact":
        return 1
    else:
        return 2


def sort_entries(entries: list[SortableChunk]) -> list[dict]:
    sorted_chunks = sorted(entries, key=lambda x: (x.juris_group,
                                                   x.result_quality,
                                                   x.primary_sort_value,
                                                   x.secondary_sort_value,
                                                   x.sub_sort_order))
    sorted_result = [chunk.data_dict for chunk in sorted_chunks]
    return sorted_result
