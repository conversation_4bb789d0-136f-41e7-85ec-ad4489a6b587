import re
from re import Pattern

INT_CHAR = "int"
ALPHA_CHAR = "alpha"
WHTSPC_CHAR = "whitespace"
PUNCT_CHAR = "punct"


def normalize_for_sorting(text: str) -> list[int]:
    normalized_output: list[int] = []

    prev_char_type: str = ""
    char_group: str = ""

    for char in text:
        char_type = get_char_type(char)
        if char_type == INT_CHAR and char_type == prev_char_type:
            char_group += char
        else:
            if char_group:
                if char_group.isdigit():
                    normalized_output.append(int(char_group))
                else:
                    normalized_output.append(ord(char_group))
            char_group = char
            prev_char_type = char_type

    if char_group:
        if char_group.isdigit():
            normalized_output.append(int(char_group))
        else:
            normalized_output.append(ord(char_group))

    return normalized_output


def get_char_type(char: str) -> str:
    if char.isdigit():
        return INT_CHAR
    elif char.isalpha():
        return ALPHA_CHAR
    elif char.isspace():
        return WHTSPC_CHAR
    else:
        return PUNCT_CHAR


def roman_to_int(s: str) -> int:
    roman_to_int_mapping = {
        'I': 1,
        'V': 5,
        'X': 10,
        'L': 50,
        'C': 100,
        'D': 500,
        'M': 1000
    }
    total = 0
    prev_value = 0
    for char in reversed(s):
        current_value = roman_to_int_mapping[char]
        if current_value >= prev_value:
            total += current_value
        else:
            total -= current_value
        prev_value = current_value
    return total


class SortableChunk:
    def __init__(self, juris_group: int, result_quality: int, data_dict: dict, primary_sort_key: str, sub_sort_order: int):
        self.data_dict = data_dict
        self.juris_group = juris_group
        self.result_quality = result_quality
        self.primary_sort_key = primary_sort_key
        self.primary_sort_value: list[int] = []
        self.secondary_sort_value: list[int] = []
        self.sub_sort_order = sub_sort_order


class BaseSortPattern:
    SORT_KEY_FIELD: str = "title"
    SORT_KEY_SPLIT_PATTERN: Pattern = re.compile(r"(\D+)\s*(\d+.*)$")
    SUB_ORDER_SORT_FIELD: str = "sub_sort_order"

    def populate_sort_values(self, chunk: SortableChunk):
        m = re.search(self.SORT_KEY_SPLIT_PATTERN, chunk.primary_sort_key)
        chunk.primary_sort_value = normalize_for_sorting(m.group(1))
        if m.group(2) is not None:
            chunk.secondary_sort_value = normalize_for_sorting(m.group(2))


class FirstLineCitationBaseSortPattern(BaseSortPattern):
    SORT_KEY_FIELD = "1st_line_citation"


class SimpleAlphaSortPattern(BaseSortPattern):
    SORT_KEY_FIELD: str = "title"
    SORT_KEY_SPLIT_PATTERN: Pattern = re.compile(r"([A-z .-]+)")

    def populate_sort_values(self, chunk: SortableChunk):
        m = re.search(self.SORT_KEY_SPLIT_PATTERN, chunk.primary_sort_key)
        chunk.primary_sort_value = normalize_for_sorting(m.group(1))


class FederalStatSortPattern(BaseSortPattern):
    SORT_KEY_FIELD = "1st_line_citation"
    SORT_KEY_SPLIT_PATTERN: Pattern = re.compile(r"(\d+)\s*.*?\s*(\d+.*)$")


class FederalComLawSortPattern(SimpleAlphaSortPattern):
    SORT_KEY_FIELD = "1st_line_citation"


class FederalConstitSortPattern(BaseSortPattern):
    SORT_KEY_FIELD = "1st_line_citation"
    SORT_KEY_SPLIT_PATTERN: Pattern = re.compile(r"(.*?)\s*([XIVLCDM]+)$")

    def populate_sort_values(self, chunk: SortableChunk):
        m = re.search(self.SORT_KEY_SPLIT_PATTERN, chunk.primary_sort_key)
        chunk.primary_sort_value = normalize_for_sorting(m.group(1))
        if m.group(2) is not None:
            chunk.secondary_sort_value = [roman_to_int(m.group(2))]
