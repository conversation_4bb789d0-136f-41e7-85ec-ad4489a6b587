import requests
import json
from app.utils.secret_retrieval_util import get_creds_from_aws
from raslogger.logging_factory import LoggingFactory
from typing import List
from app.config.settings import get_settings

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()


class OpenSearch:

    def __init__(self, secret_open_search: str, aws_region: str):
        self.secrets = get_creds_from_aws(secret_manager=secret_open_search, region=aws_region)
        self.username = self.secrets['username']
        self.password = self.secrets['password']

    def mget(self, query: str, opensearch_url: str = None):
        if opensearch_url is None:
            opensearch_url = settings.OPEN_SEARCH_ENDPOINT
        headers = {"Content-Type": "application/x-ndjson"}
        auth_header = requests.auth.HTTPBasicAuth(self.username, self.password)
        response = requests.post(f"{opensearch_url}/_mget", data=query, auth=auth_header, headers=headers)
        if response.status_code == 200:
            logger.info(f"{OpenSearch.__name__}::{OpenSearch.mget.__name__}:: open search is successful.")
            return response.json()

        logger.error(
            f"{OpenSearch.__name__}::{OpenSearch.mget.__name__}:: error {response.status_code}: {response.text}"
        )
        raise RuntimeError(f"Error {response.status_code}: {response.text}")

    @staticmethod
    def generate_mget_queries(alias: str, guids: List[str]):
        queries = []
        for guid in guids:
            query = {
                "_index": alias,
                "_id": guid,
                "_source": [
                    "doc_status",
                    "royalty_id"
                ]
            }

            queries.append(query)
        return queries

    def execute_doc_guid_open_search(self, doc_guids: list[str]) -> dict:
        all_queries = []
        alias = "statutes_metadata_v1"
        content_type_queries = self.generate_mget_queries(alias, doc_guids)
        all_queries.extend(content_type_queries)

        full_query = {"docs": all_queries}
        payload_as_str = json.dumps(full_query) + "\n"
        open_response = self.mget(payload_as_str)
        return open_response
