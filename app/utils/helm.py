import logging
import os

import yaml
import subprocess
from raslogger import LoggingFactory
from common_utils.string_builder import String<PERSON>uild<PERSON>
from tenacity import retry, wait_fixed, wait_random, before_sleep_log, stop_after_attempt

logger = LoggingFactory.get_logger(__name__)


def load_yaml(sb):
    if len(sb) == 0:
        return None
    config = yaml.safe_load(sb)
    return config


def is_version_green(version: str = os.getenv('DD_VERSION')):
    try:
        if os.getenv("ENVIRONMENT", os.getenv("DD_ENV", "local")) == "local":
            return False

        app_state = get_application_version_blue_green_state()
        if version in app_state:
            return app_state[version]
        return False
    except Exception as e:
        logger.warn('There was an issue loading the helm manifest. Defaulting to blue.')
        logger.warn(e)
        return False


@retry(stop=stop_after_attempt(10),
       wait=wait_fixed(1) + wait_random(2, 5),
       before_sleep=before_sleep_log(logger, logging.WARN),
       reraise=True)
def get_application_version_blue_green_state(env: str = os.getenv("ENVIRONMENT", os.getenv("DD_ENV", "local"))):
    output = subprocess.check_output(f"helm get manifest ai-rag-westlaw-claims-explorer-{env} -n 207891-ras-search-ai-{env}",
                                     shell=True)
    lines = output.splitlines()
    sb = StringBuilder()
    blue_green_dict = {}
    for line in lines:
        if line.decode('utf-8') == '---':
            config_yaml = load_yaml(sb.__str__())
            if config_yaml is not None and 'DestinationRule' in config_yaml['kind']:
                for deployment in config_yaml['spec']['subsets']:
                    name = deployment['name']
                    version = deployment['labels']['version']
                    is_green = 'green' in name
                    if version not in blue_green_dict:
                        blue_green_dict[version] = is_green
                    else:
                        if not blue_green_dict[version]:
                            blue_green_dict[version] = is_green
            sb = StringBuilder()
            continue
        sb.Append(line.decode('utf-8') + "\n")

    return blue_green_dict
