import traceback

from langchain.callbacks import OpenAICallbackHandler


def mils_to_time_str(millis: int) -> str:
    seconds, millis = divmod(millis, 1000)
    minutes, seconds = divmod(seconds, 60)
    return f"{minutes} minutes {seconds} seconds"


class StepMetric:
    step_name: str
    step_start_time: int
    step_end_time: int
    step_total_calls: int
    step_prompt_tokens: int
    step_completion_tokens: int
    step_total_cost: float
    step_error_dict: dict[str, int]

    def __init__(self, step_name: str, step_start_time: int):
        self.step_name = step_name
        self.step_start_time = step_start_time
        self.step_end_time = 0
        self.step_total_calls = 0
        self.step_prompt_tokens = 0
        self.step_completion_tokens = 0
        self.step_total_cost = 0
        self.step_error_dict: dict[str, int] = {}

    def aggregate_callback_results(self, callbacks: list[OpenAICallbackHandler]) -> dict:
        callback_aggregate = {"prompt_tokens": 0,
                              "completion_tokens": 0,
                              "total_calls": 0,
                              "total_cost": 0.00}
        for callback in callbacks:
            callback_aggregate["total_calls"] += 1
            callback_aggregate["prompt_tokens"] += callback.prompt_tokens
            callback_aggregate["completion_tokens"] += callback.completion_tokens
            callback_aggregate["total_cost"] += callback.total_cost

        self.step_total_calls = callback_aggregate["total_calls"]
        self.step_prompt_tokens = callback_aggregate["prompt_tokens"]
        self.step_completion_tokens = callback_aggregate["completion_tokens"]
        self.step_total_cost = callback_aggregate["total_cost"]

        return callback_aggregate

    def aggregate_exceptions(self, exception_list: list[Exception]):
        temp_error_dict = {}
        for exception in exception_list:
            exception_name = exception.__class__.__name__
            if exception_name in temp_error_dict:
                temp_error_dict[exception_name] = temp_error_dict[exception_name] + 1
            else:
                temp_error_dict[exception_name] = 1

        self.step_error_dict = temp_error_dict

    def formatted_step_metrics(self) -> str:
        step_error_dict: dict[str, int]
        step_duration = self.step_end_time - self.step_start_time
        step_duration_minutes = mils_to_time_str(step_duration)
        
        # Prevent division by zero by checking if step_duration is zero or very small
        if step_duration > 0:
            step_token_rate = ((self.step_prompt_tokens + self.step_completion_tokens) / (step_duration / 60000))
        else:
            step_token_rate = 0  # Default to 0 if duration is zero

        formatted_metrics = f"==**== Step Metrics: {self.step_name} ==**==\n" \
                            f" Step Duration: {step_duration_minutes}\n" \
                            f" Total Calls: {self.step_total_calls}\n" \
                            f" Prompt Tokens: {self.step_prompt_tokens}\n" \
                            f" Completion Tokens: {self.step_completion_tokens}\n" \
                            f" Token Usage Rate: {step_token_rate} tokens/min\n" \
                            f" Total Cost: ${self.step_total_cost}\n"

        if self.step_error_dict:
            formatted_metrics += f"## /!\\ Step Exceptions /!\\ ##\n"

        for ex, count in self.step_error_dict.items():
            formatted_metrics += f" {ex}: {count} occurrences\n"

        return formatted_metrics


class OpenAIMetrics:
    conversation_id: str
    conversation_entry_id: str
    jurisdictions: list[str]
    job_start_time: int
    job_end_time: int
    claim_metrics: dict[str, int]
    step_metrics: list[StepMetric]

    def __init__(self, conversation_id: str, conversation_entry_id: str, jurisdictions: list[str], job_start_time: int):
        self.conversation_id = conversation_id
        self.conversation_entry_id = conversation_entry_id
        self.jurisdictions = jurisdictions
        self.job_start_time = job_start_time
        self.job_end_time = 0
        self.claim_metrics: dict[str, int] = {}
        self.step_metrics: list[StepMetric] = []

    def formatted_metrics(self, input_question: str, end_state: str, final_exception: Exception = None) -> str:
        job_duration = self.job_end_time - self.job_start_time
        job_duration_minutes = mils_to_time_str(job_duration)
        formatted_metrics = (f"==**== Claims Explorer Job Metrics ==**==\n"
                             f" Conversation ID: {self.conversation_id} - Conversation Entry ID: {self.conversation_entry_id}\n"
                             f" Jurisdictions: {self.jurisdictions}\n"
                             f" Input Question: {input_question}\n "
                             f" Job End State: {end_state}\n "
                             f" Job Duration: {job_duration_minutes}\n")

        if self.claim_metrics:
            formatted_metrics += (f"==**== Claim Result Metrics ==**==\n"
                                  f" Total Claim Results: {sum(self.claim_metrics.values())} results\n"
                                  f"** Claim Metrics Breakdown **\n")

            for claim_key, count in self.claim_metrics.items():
                formatted_metrics += f" {claim_key}: {count} results\n"

        if final_exception:
            formatted_metrics += (f" Job Fatal Exception: {final_exception.__class__.__name__}"
                                  f"::{traceback.format_tb(final_exception.__traceback__)}\n")

        for step in self.step_metrics:
            formatted_metrics += step.formatted_step_metrics()

        return formatted_metrics
