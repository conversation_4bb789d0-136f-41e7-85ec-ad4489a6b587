import json
import csv
from csv import DictReader
import random
from pathlib import Path

from raslogger import LoggingFactory
import pandas as pd
from pandas import DataFrame
import traceback
import re
from config.settings import get_settings

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()


def dicts_to_dataframe(json_dicts: list[dict]) -> DataFrame:
    df: DataFrame
    try:
        df = pd.DataFrame(json_dicts)
    except Exception as e:
        logger.error(f"{dicts_to_dataframe.__name__}:: {type(e).__name__} exception occurred {e}; "
                     f"{traceback.format_tb(e.__traceback__)} ")
        raise e
    return df


def read_csv_to_json(file_path: str) -> list[dict]:
    csv_rows = []
    try:
        path = Path(file_path).absolute()
        with open(path, 'r', encoding='utf-8-sig') as file:

            csv_reader: DictReader = csv.DictReader(file, delimiter=',', dialect='excel')
            fields = csv_reader.fieldnames
            row: dict
            for row in csv_reader:
                row_dict = {}
                for i in range(len(fields)):
                    key = fields[i]
                    if key in row and row[key]:
                        row_dict[key] = row[key]
                    else:
                        row_dict[key] = None
                csv_rows.append(row_dict)
    except FileNotFoundError as fnfe:
        logger.error(f"{read_csv_to_json.__name__}:: {type(fnfe).__name__} file not found at {fnfe.filename}; "
                     f"{traceback.format_tb(fnfe.__traceback__)}")
    except Exception as e:
        logger.error(f"{read_csv_to_json.__name__}:: {type(e).__name__} exception occurred {e}; "
                     f"{traceback.format_tb(e.__traceback__)} ")
    return csv_rows


def mock_randomize_categorization_options(filter_check_string: str) -> list[str]:
    randomized_min_facts = []
    for min_fact in filter_check_string.split("\\n"):
        if random.randrange(20) > 18:
            randomized_min_facts.append(min_fact)

    return randomized_min_facts


def mock_randomize_filter_responses(cause_grouping: list[dict]) -> list[dict]:
    formatted_responses: list[dict] = []
    for cause in cause_grouping:
        response_dict = {"ClaimsID": cause['ClaimsID']}
        i = random.randrange(20)
        if i > 18:
            response_dict["Answer"] = "Yes"
        elif i > 16:
            response_dict["Answer"] = "Possibly"
        else:
            response_dict["Answer"] = "No"
        formatted_responses.append(response_dict)
    return formatted_responses


def mock_randomize_answer_responses(cause_input: str) -> dict:
    pattern = r"'ClaimsID':\s'([^']*)'"

    match = re.search(pattern, cause_input)

    claims_id = match.group(1)

    response_dict = {"ClaimsID": claims_id}

    i = random.randrange(20)
    if i > 18:
        response_dict["Answer"] = "Yes"
    elif i > 16:
        response_dict["Answer"] = "Possibly with Additional Fact"
    else:
        response_dict["Answer"] = "No"

    response_dict["Discussion"] = "This is a mock response generated for Claims Explorer"

    return response_dict


def mock_randomize_answer_responses_v2(input_claims: list[dict]) -> list[dict]:

    explanation_outputs: list[dict] = []

    for claim in input_claims:
        explanation_dict: dict = {"ClaimsID": claim["ClaimsID"]}
        i = random.randrange(20)
        if i > 18:
            explanation_dict["answer"] = "Yes"
        elif i > 16:
            explanation_dict["answer"] = "Possibly with Additional Fact"
        else:
            explanation_dict["answer"] = "No"

        explanation_dict["thoughts"] = "This is a mock response generated for Claims Explorer"
        explanation_outputs.append(explanation_dict)

    return explanation_outputs
