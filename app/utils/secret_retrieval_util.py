import json
import boto3


def get_creds_from_aws(secret_manager: str, region: str) -> dict:
    client = boto3.client('secretsmanager', region_name=region)
    secret_value = client.get_secret_value(SecretId=secret_manager)

    if "SecretString" in secret_value:
        secret_value = secret_value["SecretString"]
    else:
        secret_value = secret_value["SecretBinary"]
    secret_value = json.loads(secret_value)

    return secret_value
