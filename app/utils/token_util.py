import time
from app.config.settings import ClaimExRAGSettings
from app.utils.secret_retrieval_util import get_creds_from_aws
from raslogger import LoggingFactory
from azure.identity import ClientSecretCredential

logger = LoggingFactory.get_logger(__name__)


class OpenAIToken:
    def __init__(self, token: str = None, token_expiration_time: int = None):
        if token is None:
            self.token = ""
        else:
            self.token = token
        if token_expiration_time is None:
            self.token_expiration_time = 0
        else:
            self.token_expiration_time = token_expiration_time

    def is_expired(self) -> bool:
        if not self.token:
            return True
        now_time = int(time.time())
        # make sure there is at least 20 minutes left on token
        token_is_expired = self.token_expiration_time - 1200 < now_time
        logger.debug(
            f"TOKEN TIMING CHECK: token expires at {self.token_expiration_time} and right now it is {now_time}")
        if token_is_expired:
            logger.info(f"Token is expired or near expiring: expiration at {self.token_expiration_time}")
        return token_is_expired

    def refresh_openai_token(self, rag_settings: ClaimExRAGSettings):
        if not self.is_expired():
            logger.info("OpenAI token is not expired. No need to refresh.")
            return

        logger.debug("Refreshing OpenAI token")
        logger.info(f"Getting secrets from {rag_settings.OPEN_AI_SECRET} in region {rag_settings.REGION}")
        secret_values: dict = get_creds_from_aws(secret_manager=rag_settings.OPEN_AI_SECRET,
                                                 region=rag_settings.REGION)

        try:
            tenant_id = secret_values["AZURE_TENANT_ID"]
            client_id = secret_values["AZURE_CLIENT_ID"]
            client_secret = secret_values["AZURE_CLIENT_SECRET"]
        except KeyError as ke:
            logger.error(f"{OpenAIToken.__name__}::{type(ke).__name__} exception occurred {ke}; ")
            raise

        credential = ClientSecretCredential(tenant_id, client_id, client_secret)
        response = credential.get_token("https://cognitiveservices.azure.com/.default")
        logger.info(f"Token refreshed. Token expires at: {response.expires_on}")
        self.token = response.token
        self.token_expiration_time = response.expires_on
