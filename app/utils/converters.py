from typing import Optional

from conversation_core.shared.enums import IntentClassification
from conversation_core.shared.enums import RetrieveConversationEntryStatuses, \
    ConversationActionType
from aalp_service.v2.intent_resolver.dataclasses import AnnotationType

def convert_conversation_entries_to_conversation_entries(conversation_history,
                                                         intent_model: str = None,
                                                         rag_model: str = None):
    ret_conversation_history = []
    conversation_history.sort(key=lambda entry: entry.timestamp)
    for conversation_entry in conversation_history:
        if (conversation_entry.status == RetrieveConversationEntryStatuses.COMPLETE
                and conversation_entry.result is not None):
            if conversation_entry.result.conversation_action_type == ConversationActionType.RAG:
                if rag_model is None:
                    if conversation_entry.result.intermediate_results is not None:
                        ret_conversation_history.append(conversation_entry.result.intermediate_results)
                else:
                    if conversation_entry.result.system_output is not None and "rag_pipeline_output" in conversation_entry.result.system_output:
                        history_entry: dict = {"type": rag_model,
                                               "data": conversation_entry.result.system_output["rag_pipeline_output"]}
                        history_entry["data"]["user_input"]["content_types"] = \
                        conversation_entry.result.system_output["rag_pipeline_output"]["intent"]["user_input"][
                            "content_types"]  # Labs input has strict user input requirements
                        ret_conversation_history.append(history_entry)
            if (intent_model is not None
                    and conversation_entry.result.conversation_action_type == ConversationActionType.INTENT):
                if conversation_entry.result.system_output is not None:
                    history_entry: dict = {"type": intent_model, "data": conversation_entry.result.system_output}
                    ret_conversation_history.append(history_entry)
    return ret_conversation_history

def convert_annotation_to_intent_classification(annotation: AnnotationType) -> Optional[IntentClassification]:
    try:
        return IntentClassification[annotation.name]
    except KeyError:
        return None