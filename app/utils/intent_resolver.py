from typing import Any, Dict, List

from aalp_service.v2.intent_resolver.common import QueryLabel
from aalp_service.v2.intent_resolver.config import IntentResolverSettings
from aalp_service.v2.intent_resolver.dataclasses import (
    IntentResolverServiceOutput,
    AnnotationType
)
from aalp_service.v2.intent_resolver.service import IntentResolverService
from conversation_core.shared.constants import Constants
from conversation_core.shared.models.intent_resolver import IntentResolverOutput
from ddtrace import tracer
from pydantic_settings import BaseSettings
from ras_events_client.event_service import send_event
from ras_events_client.models.event_models import BaseProperties, create_event_v2
from raslogger import LoggingFactory

from utils.custom_exceptions import MaliciousContentException

logger = LoggingFactory.get_logger(__name__)


class IntentResolverAnnotationProperties(BaseProperties):
    user_input: dict
    annotations: list[dict]


class IntentResolverProperties(BaseProperties):
    question: str
    intent_category: str
    is_sufficient: bool


class IntentResolverStartProperties(BaseProperties):
    question: str


def copy_model_settings(model_settings_to_copy: BaseSettings,
                        llm_type_header_append_str: str) -> BaseSettings:
    ret = model_settings_to_copy.model_copy(deep=True)
    if "CUSTOM_HEADERS" in ret and Constants.LLM_TYPE_HEADER in ret.CUSTOM_HEADERS:
        ret.CUSTOM_HEADERS[Constants.LLM_TYPE_HEADER] += llm_type_header_append_str
    return ret


@tracer.wrap()
async def check_intent(profile: str,
                       message: str,
                       content_types: List[str],
                       fermi_jurisdictions: List[str],
                       conversation_history: List[Dict],
                       intent_settings: IntentResolverSettings,
                       raise_exceptions: bool) -> IntentResolverServiceOutput:
    intent_response: IntentResolverServiceOutput = await IntentResolverService.evaluate(
        profile=profile,
        message=message,
        content_types=content_types,
        fermi_jurisdictions=fermi_jurisdictions,
        conversation_history=conversation_history,
        settings=intent_settings)

    event = create_event_v2(conversation_id=intent_settings.conversation_id,
                            conversation_entry_id=intent_settings.conversation_entry_id,
                            profile=profile,
                            user_session_dict=intent_settings.cobalt_settings.COBALT_SESSION,
                            properties=IntentResolverAnnotationProperties,
                            action_type="rag",
                            input_obj={
                                "user_input": intent_response.user_input.model_dump(exclude_none=True,
                                                                                    exclude_unset=True),
                                "annotations": [a.model_dump(exclude_none=True, exclude_unset=True) for a in
                                                intent_response.annotations]
                            },
                            event_name="RASSearch.AI.IntentResolverResult.v2", )
    send_event(body=event, auth_token=intent_settings.auth_token, wait=True)

    if "-editorial" in intent_settings.tags[0].lower():
        return intent_response

    for annotation in intent_response.annotations:
        if annotation.type == AnnotationType.IS_UNDERSPECIFIED:
            if annotation.value:
                error_message: str = (f"Intent Resolver is not sufficient for question: {message} "
                                      f":: {intent_response.model_dump()}")
                logger.info(error_message)
                if raise_exceptions:
                    raise MaliciousContentException(error_message)
        if annotation.type == AnnotationType.ADVERSARIAL:
            if annotation.value:
                error_message: str = (f"Intent Resolver is found to be ADVERSARIAL for question: {message} "
                                      f":: {intent_response.model_dump()}")
                logger.info(error_message)
                if raise_exceptions:
                    raise MaliciousContentException(error_message)
        if annotation.type == AnnotationType.QUERY_VALIDATION_ERROR:
            if annotation.value:
                error_message: str = (f"Intent Resolver is found to be QUERY_VALIDATION_ERROR for question: {message} "
                                      f":: {intent_response.model_dump()}")
                logger.info(error_message)
                if raise_exceptions:
                    raise MaliciousContentException(error_message)
    return intent_response
