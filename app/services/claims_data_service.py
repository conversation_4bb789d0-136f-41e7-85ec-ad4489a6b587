import re
import random
import uuid

from re import Pattern

from raslogger import LoggingFactory
from pandas import DataFrame
from app.utils.common_util import read_csv_to_json, dicts_to_dataframe

from itertools import product
from string import ascii_uppercase

from app.config.settings import get_settings

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()


def load_claims() -> [DataFrame, dict]:

    # Last Amended Formatted acceptable date patterns to validate against
    date_pattern1 = r'\d{1,2}/\d{1,2}/\d{4}'
    date_pattern2 = r'\d{4}'

    try:
        raw_claims = read_csv_to_json("app/resources/LatestClaims/ClaimEx_Worksheet.csv")

        claims: list[dict] = []
        alpha_id_to_uuid_map: dict = {}
        # Generate a list of alpha IDs to use to reduce token usage...
        # appending ZX tends to cause GPT to not confuse the IDs with other text, and increases the likelihood that
        # the ID responses will be correct
        letter_set = "ABCDEFGHIJKLMNPQRSTUVWY"
        number_set = "123456789"

        alpha_id_list = ["ZX_" + a + b + c + d for a, b, c, d in
                         product(letter_set, number_set, letter_set, number_set)]
        random.shuffle(alpha_id_list)

        # A bit of data validation and clean up here...
        i = 0
        for claim in raw_claims:
            for key in claim:
                if claim[key] is not None:
                    claim[key] = normalize_whitespace(claim[key])

            claims_id = claim['ClaimsID']
            if not claims_id:
                claims_id = str(uuid.uuid4())
                claim['ClaimsID'] = claims_id

            if not claim.get('sourceLaw'):
                logger.warn(f"Claim in spreadsheet with id {claims_id} has no sourceLaw! Discarding this entry!")
                continue
            else:
                claim['sourceLaw'] = claim['sourceLaw'].strip().lower()
                source_law = claim['sourceLaw']

            if not claim.get('StatuteTitle'):
                logger.warn(f"Claim in spreadsheet with id {claims_id} has no StatuteTitle! Discarding this entry!")
                continue
            if not claim.get('1stLineCitation') and source_law != "common law":
                logger.warn(f"Claim in spreadsheet with id {claims_id} has no 1stLineCitation! Discarding this entry!")
                continue
            if not claim.get('subsection_cite') and source_law != "common law":
                logger.warn(f"Claim in spreadsheet with id {claims_id} has no subsection_cite! Discarding this entry!")
                continue

            sub_sort_order = claim['sub_sort_order']
            if not sub_sort_order:
                claim['sub_sort_order'] = '1'
            alpha_id = alpha_id_list[i]
            alpha_id_to_uuid_map[alpha_id] = claims_id
            claim['ClaimsID'] = alpha_id
            i += 1

            if not claim['minimum_fact']:
                claim['minimum_fact'] = "none"

            if claim['last_amended_formatted']:
                laf_str = claim['last_amended_formatted']
                if not bool(re.search(date_pattern1, laf_str) or re.search(date_pattern2, laf_str)):
                    logger.warning(f"Claim in spreadsheet with id {claims_id} has malformed last_amended_formatted value {laf_str}!")
                    claim['last_amended_formatted'] = None

            # FIXME: Temporary measure to normalize data better from the spreadsheet; ideally the data should be fixed
            # when we switch over to getting the data from Codes Workbench
            if claim['StatuteTitle']:
                claim['StatuteTitle'] = normalize_title(claim['StatuteTitle'])
            if claim['1stLineCitation']:
                claim['1stLineCitation'] = normalize_cite(claim['1stLineCitation'])
            if claim['subsection_cite']:
                claim['subsection_cite'] = normalize_cite(claim['subsection_cite'])

            claims.append(claim)

        return [dicts_to_dataframe(claims), alpha_id_to_uuid_map]
    except Exception as e:
        logger.error(f"Error loading claims: {e}")
        raise e


def normalize_whitespace(text: str) -> str:
    return text.replace(u"\u00A0", " ").replace(u"\u2009", "")


def normalize_title(title: str) -> str:
    normalized_title = title
    section_pattern: Pattern = re.compile(r'^([S§ .]+)(\d+.*)$')
    section_pattern_match = re.search(section_pattern, normalized_title)

    if section_pattern_match:
        normalized_title = f"§ {section_pattern_match.group(2)}"

    return normalized_title


def normalize_cite(cite: str) -> str:
    normalized_cite = cite
    usca_pattern: Pattern = re.compile(r'^(\d+)\s+(U\.?S\.?C\.?|USCA)\s+(\d+.*)$')
    usca_pattern_match = re.search(usca_pattern, normalized_cite)

    if usca_pattern_match:
        normalized_cite = f"{usca_pattern_match.group(1)} U.S.C.A. {usca_pattern_match.group(3)}"

    section_pattern: Pattern = re.compile(r'^(.*?)\s+[S§ .]+(\d+.*)$')
    section_pattern_match = re.search(section_pattern, normalized_cite)

    if section_pattern_match:
        normalized_cite = f"{section_pattern_match.group(1)} § {section_pattern_match.group(2)}"

    return normalized_cite


class ClaimsDataService:
    def __init__(self):
        loaded_claims = load_claims()
        self.claims_data: DataFrame = loaded_claims[0]
        self.alpha_id_to_uuid_map = loaded_claims[1]

    def get_all_records(self) -> list[dict]:
        claims_df = self.claims_data
        claims_records = claims_df.to_dict('records')
        return claims_records

    def get_records_by_claims_id(self, claims_id: str) -> list[dict]:
        claims_df = self.claims_data
        claims_records = claims_df.loc[claims_df['ClaimsID'] == claims_id].to_dict('records')
        return claims_records

    def get_claims_id_from_alpha_id(self, alpha_id: str) -> str:
        claims_id = self.alpha_id_to_uuid_map[alpha_id]
        return claims_id

    def get_source_law_by_claim_id(self, claim_id: str) -> str:
        claims_df = self.claims_data
        source_law = claims_df.loc[claims_df['ClaimsID'] == claim_id, 'sourceLaw'].values[0]
        return source_law

    def get_jurisdiction_by_claim_id(self, claim_id: str) -> str:
        claims_df = self.claims_data
        jurisdiction = claims_df.loc[claims_df['ClaimsID'] == claim_id, 'Jurisdiction'].values[0]
        return jurisdiction

    def get_distinct_minimum_facts(self) -> list[str]:
        claims_df = self.claims_data
        unique_min_facts = claims_df['minimum_fact'].unique().tolist()
        return unique_min_facts

    def get_distinct_minimum_facts_for_jurisdictions(self, jurisdictions: list[str]) -> list[str]:
        claims_df = self.claims_data
        unique_min_facts = claims_df.loc[
            claims_df['Jurisdiction'].isin(jurisdictions), 'minimum_fact'].unique().tolist()
        return unique_min_facts

    def get_statutes_by_jurisdiction(self, filter_strings: list[str], jurisdiction: str) -> list[dict]:
        desired_columns = ["ClaimsID", "StatuteTitle", "subsection_cite", "long_description",
                           "short_description"]
        actionable_values = ["Y", "R", "U"]
        minimum_facts = filter_strings
        if filter_strings == ["None", "None"]:
            minimum_facts.append("none")
        elif filter_strings == "ALL":
            pass
        else:
            minimum_facts.append("none")

        jurisdiction_statutes = self.query_statutes_by_jurisdiction(desired_columns=desired_columns,
                                                                    jurisdiction=jurisdiction,
                                                                    minimum_facts=minimum_facts,
                                                                    actionable_values=actionable_values)

        return jurisdiction_statutes

    def query_statutes_by_jurisdiction(self, desired_columns: list[str], jurisdiction: str,
                                       minimum_facts: list[str], actionable_values: list[str]) -> list[dict]:
        claims_df = self.claims_data

        # Escape special regex characters in minimum_facts to avoid regex syntax errors
        escaped_min_facts = [re.escape(fact) for fact in minimum_facts]
        min_fact_str = '|'.join(escaped_min_facts)
        logger.info(f"Querying statutes for jurisdiction {jurisdiction} with minimum facts: {min_fact_str}")

        # Filtering queries to run on dataframe
        q_juris = claims_df['Jurisdiction'] == jurisdiction
        q_action = claims_df['actionable'].isin(actionable_values)
        q_min_fact = claims_df['minimum_fact'].str.lower().str.contains(min_fact_str)

        # Run the queries here, retrieving only the desired columns
        results: list[str] = claims_df.loc[q_juris & q_action & q_min_fact, desired_columns].values

        formatted_results: list[dict] = []
        for result in results:
            formatted_results.append({"ClaimsID": result[0],
                                      "Title": result[1],
                                      "Citation": result[2],
                                      "Long Desc": result[3],
                                      "Short Desc": result[4]}
                                     )

        return formatted_results

    def generate_alpha_id(self):
        result = 'ZX_'
        result += random.choice('ABCDEFGHIJKLMNPQRSTUVWY')
        result += (str(random.randint(0, 9)))
        result += random.choice('ABCDEFGHIJKLMNPQRSTUVWY')
        result += (str(random.randint(0, 9)))
        return result
