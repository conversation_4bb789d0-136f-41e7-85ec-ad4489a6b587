import time
import asyncio
import traceback
from asyncio import Future, CancelledError
from typing import Optional, List, Any, Union

from conversation_core.shared.models.intent_resolver import IntentResolverOutput
from aalp_service.v2.intent_resolver.dataclasses import AnnotationType, IntentResolverServiceOutput
from conversation_core.shared.constants import Constants
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.worker.worker_task import WorkerTask
from conversation_core.shared.services.intent_resolver_base import IntentResolverServiceBaseV3
from raslogger import LoggingFactory
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from conversation_core.shared.enums import ConversationActionType, RetrieveConversationEntryStatuses
from app.config.settings import get_settings, map_to_intent_settings
from app.worker.conversation_tasks import ConversationTaskV2
from services.answer_profile_service import answer_profile_service
from utils.intent_resolver import check_intent as intent_resolver_check_intent
from utils.converters import (
    convert_conversation_entries_to_conversation_entries,
    convert_annotation_to_intent_classification
)
from aalp_service.v2.intent_resolver.config import IntentResolverSettings

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
conversation_v2 = ConversationTaskV2()

out_of_scope_intents = [
    AnnotationType.CITATION_REQUESTED,
    AnnotationType.REQUESTED_CITATION_STYLE,
    AnnotationType.DOCUMENT_INTENT,
    AnnotationType.CONTAINS_ANALYTIC_REQUEST,
    AnnotationType.MULTIPLE_JURISDICTIONS_REQUESTED,
    AnnotationType.JUDGE_REQUSTED,
    AnnotationType.LOCAL_RULES_REQUESTED,
    AnnotationType.ASKS_FOR_PREDICTION_OR_OUTCOME,
    AnnotationType.PARTY_REQUESTED,
    AnnotationType.SUMMARIZE_LAW_REQUESTED
]


class IntentResolverService(IntentResolverServiceBaseV3):
    def __init__(self):
        super().__init__(settings=settings,
                         dynamo_db_v2=dynamo_db_v2,
                         answer_profile_service=answer_profile_service)
        logger.info("Initiated IntentResolverServiceBase V3")

    async def check_intent(
            self,
            is_new_conversation: bool,
            user_id: str,
            user_input: str,
            answer_solution_profile: str,
            jurisdictions_override: Optional[List[str]],
            content_types_override: Optional[List[str]],
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            worker_task: WorkerTask,
            answer_profile: AnswerProfile,
            tags: Optional[List[str]] = None,
            user_session: dict = None,
            meta_data: dict = None,
            conversation_history: List[dict] = None,
    ) -> Any:
        try:
            if not is_new_conversation:
                if conversation_history is None:
                    conversation = self.dynamo_db_v2.get_conversation(
                        user_id=user_id, conversation_id=conversation_id, include_intermediate_results=True
                    )
                    conversation_history = conversation.conversation_entries
                    conversation_history = convert_conversation_entries_to_conversation_entries(
                        conversation_history=conversation_history,
                        intent_model="IntentResolverServiceOutput",
                        rag_model="RagServiceOutput")

            intent_settings: IntentResolverSettings = map_to_intent_settings(settings=settings,
                                                                             auth_token=auth_token,
                                                                             solution_profile=answer_profile.rag_solution,
                                                                             answer_profile=answer_profile,
                                                                             ras_profile=answer_solution_profile.lower(),
                                                                             conversation_id=conversation_id,
                                                                             conversation_entry_id=conversation_entry_id,
                                                                             cobalt_session=user_session,
                                                                             tags=tags,
                                                                             metadata=meta_data)

            loop = asyncio.get_event_loop()
            tasks = []
            try:
                # Supply defaults if not overrides
                content_types = content_types_override if content_types_override else answer_profile.default_content_types
                fermi_jurisdictions = jurisdictions_override if jurisdictions_override else answer_profile.default_fermi_jurisdiction

                intent_resolver_task = loop.create_task(
                    intent_resolver_check_intent(
                        profile=answer_profile.intent_profile,
                        message=user_input,
                        content_types=content_types,
                        fermi_jurisdictions=fermi_jurisdictions,
                        conversation_history=conversation_history,
                        intent_settings=intent_settings,
                        raise_exceptions=False
                    )
                )
                tasks.append(intent_resolver_task)
                future_tasks: Future = asyncio.gather(*tasks)
                while not future_tasks.done() and not future_tasks.cancelled():
                    await asyncio.sleep(5)
                    logger.debug("Waited 5 seconds")
                    conversation_entry = dynamo_db_v2.get_conversation_entry(
                        user_id=user_id,
                        conversation_id=conversation_id,
                        conversation_entry_id=conversation_entry_id)
                    if conversation_entry.status == RetrieveConversationEntryStatuses.CANCELLING.value:
                        logger.info("Celery worker task has been revoked, cancelling asyncio task(s)")
                        raise CancelledError(f"Cancellation request issued by user for "
                                             f"conversation {conversation_id} {conversation_entry_id}")
                intent_resolver_result: IntentResolverServiceOutput = intent_resolver_task.result()

                # FIXME: This is a temporary measure put in until App can properly handle these annotation from intent
                for annotation in intent_resolver_result.annotations:
                    if annotation.type in out_of_scope_intents:
                        intent_resolver_result.annotations.remove(annotation)

                return intent_resolver_result
            except (CancelledError, Exception) as ex:
                logger.error(f"{IntentResolverService.__name__}::{type(ex).__name__} exception occurred {ex} while "
                             f"executing async tasks; {traceback.format_tb(ex.__traceback__)}")
                for task in tasks:
                    if task and not task.cancelled():
                        task.cancel()
                raise ex
        except CancelledError as cancel_error:
            dynamo_db_v2.update_conversation_entry(
                user_id=user_id,
                conversation_id=conversation_id,
                conversation_entry_id=conversation_entry_id,
                attribute_updates={Constants.CONV_STATUS: RetrieveConversationEntryStatuses.CANCELLED.value}
            )
            logger.info(f"Conversation Entry Status set to {RetrieveConversationEntryStatuses.CANCELLED.value}")
            raise cancel_error

    def auto_submit_intent(
            self,
            is_new_conversation: bool,
            user_id: str,
            user_input: str,
            answer_solution_profile: str,
            jurisdictions_override: Optional[List[str]],
            content_types_override: Optional[List[str]],
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            worker_task: WorkerTask,
            answer_profile: AnswerProfile,
            user_session: dict = None,
            meta_data: dict = None,
    ):
        conversation_v2.start_conversation_task.apply_async(
            args=[
                is_new_conversation,
                user_id,
                user_input,
                answer_solution_profile,
                jurisdictions_override,
                content_types_override,
                conversation_id,
                conversation_entry_id,
                ConversationActionType.RAG,
                auth_token,
                user_session,
                meta_data,
            ],
            kwargs=None,
            task_id=conversation_entry_id,
            headers={
                "time_sent": time.time(),
                "conversation_id": conversation_id,
                "conversation_entry_id": conversation_entry_id,
                "user_classification": meta_data.get("user_classification", "unknown"),
                "green": meta_data.get("route_to_green", False),
            },
        )

    def handle_error(
            self,
            user_id: str,
            conversation_id: str,
            conversation_entry_id: str,
            answer_profile: AnswerProfile,
            meta_data: dict,
            ex: Exception,
    ):
        pass

    def is_intent_category_sufficient_to_submit(self,
                                                intent_response: Any,
                                                profile: AnswerProfile,
                                                meta_data: dict):
        if meta_data is not None and not meta_data.get('auto_submit_task', True):
            return False
        if profile.auto_submit_intent_classifications is None or len(profile.auto_submit_intent_classifications) == 0:
            return False

        if hasattr(intent_response, "is_sufficient") and not intent_response.is_sufficient:
            return False
        if hasattr(intent_response,
                   "intent_category") and intent_response.intent_category in profile.auto_submit_intent_classifications:
            return True
        if hasattr(intent_response, "annotations"):
            for annotation in intent_response.annotations:
                if annotation.type == AnnotationType.IS_UNDERSPECIFIED and annotation.value:
                    return False
                if annotation.type == AnnotationType.QUERY_VALIDATION_ERROR and annotation.value:
                    return False
        return True
