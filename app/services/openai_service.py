import os
import asyncio
import sys
import traceback
import time
import re
from asyncio import Semaphore
from app.config.settings import ClaimExRAGSettings, Settings
from config import settings

from langchain_openai.chat_models import AzureChatOpenAI
from langchain.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate
)
from openai import RateLimitError, APITimeoutError, InternalServerError
from langchain.callbacks import get_openai_callback, OpenAICallbackHandler
from typing import Optional
from langchain.output_parsers.json import SimpleJsonOutputParser
from langchain.schema.runnable import RunnableSerializable
from common_utils.time_utils import current_time_millis, total_time_millis
from raslogger import LoggingFactory
from app.model import constants as const
from app.model.claims_explorer_result import ClaimsExplorerResult
from app.services.claims_data_service import ClaimsDataService
from app.utils.token_util import OpenAIToken
from app.utils.open_search_util import OpenSearch
from app.utils.custom_exceptions import *
from app.utils.sorting.sort_util import do_sort
from app.utils.open_ai_metrics import OpenAIMetrics, StepMetric
from utils.common_util import (
    mock_randomize_categorization_options,
    mock_randomize_filter_responses,
    mock_randomize_answer_responses
)
from utils.editorial_util import split_json_question_editorial_claim, user_input_to_json_editorial
from conversation_core.shared.constants import Constants

from conversation_core.shared.worker.worker_task import WorkerTask

logger = LoggingFactory.get_logger(__name__)

claims_data_service = ClaimsDataService()
settings: Settings = settings.get_settings()

# if llm-proxy is enabled, no need to create the azure token. llm-proxy will handle it
api_token = "__none__" if settings.LLM_PROXY_ENABLED else ""
token_exp = sys.maxsize if settings.LLM_PROXY_ENABLED else 0

token = OpenAIToken(token=api_token, token_expiration_time=token_exp)

run_env = os.getenv("ENVIRONMENT", "local")

GPT_3_5_MAX_CONCURRENCY = 10 if run_env != "local" else 5
GPT_4_MAX_CONCURRENCY = 20 if run_env != "local" else 16
CLAIMEX_MAX_RUNTIME = 15 if run_env != "local" else 30
RATE_LIMIT_SLEEP = 5

logger.info(f"Pod in environment {run_env} started with GPT 3.5 concurrency {GPT_3_5_MAX_CONCURRENCY}, "
            f"GPT 4 concurrency {GPT_4_MAX_CONCURRENCY} and max runtime {CLAIMEX_MAX_RUNTIME} minutes")


class OpenAIService:

    async def claims_explorer(self, rag_settings: ClaimExRAGSettings, conversation_id: str, conversation_entry_id: str,
                              question: str, worker_task: WorkerTask, jurisdictions: list[str],
                              is_mock_request: bool) -> ClaimsExplorerResult:

        try:
            if "-editorial" in rag_settings.tags[0].lower():
                claimex_task = asyncio.create_task(self.do_ai_claims_editorial(rag_settings=rag_settings,
                                                                               conversation_id=conversation_id,
                                                                               conversation_entry_id=conversation_entry_id,
                                                                               question=question,
                                                                               worker_task=worker_task,
                                                                               jurisdictions=jurisdictions,
                                                                               is_mock_request=is_mock_request))

                result = await asyncio.wait_for(claimex_task, CLAIMEX_MAX_RUNTIME * 60)
            else:
                claimex_task = asyncio.create_task(self.do_ai_claims_exploration(rag_settings=rag_settings,
                                                                                 conversation_id=conversation_id,
                                                                                 conversation_entry_id=conversation_entry_id,
                                                                                 question=question,
                                                                                 worker_task=worker_task,
                                                                                 jurisdictions=jurisdictions,
                                                                                 is_mock_request=is_mock_request))

                result = await asyncio.wait_for(claimex_task, CLAIMEX_MAX_RUNTIME * 60)
        except asyncio.TimeoutError:
            raise ClaimsExplorationTimeoutException(
                f"Claims Exploration task timed out after {CLAIMEX_MAX_RUNTIME} minutes!")

        return result

    async def do_ai_claims_editorial(self, rag_settings: ClaimExRAGSettings, conversation_id: str,
                                     conversation_entry_id: str, question: str, worker_task: WorkerTask,
                                     jurisdictions: list[str], is_mock_request: bool) -> ClaimsExplorerResult:
        job_metrics = OpenAIMetrics(conversation_id=conversation_id,
                                    conversation_entry_id=conversation_entry_id,
                                    jurisdictions=jurisdictions,
                                    job_start_time=current_time_millis())

        logger.info(f"==**== Claims Explorer Editorial Task Started ==**==\n"
                    f" Conversation ID: {conversation_id} - Conversation Entry ID: {conversation_entry_id}\n"
                    f" Jurisdictions: {jurisdictions}\n"
                    f" Input Question: {question}\n ")

        try:
            token.refresh_openai_token(rag_settings=rag_settings)

            remapped_jurisdictions = self.remap_jurisdictions(jurisdictions)

            worker_task.update_task_status_in_progress(percent_complete=5,
                                                       status_desc="Claims Explorer task is filtering results!")

            # Parse the user input for the claims information.
            filter_start_time = current_time_millis()
            inputs = split_json_question_editorial_claim(question)
            cause_group = user_input_to_json_editorial(inputs[0])
            user_question = inputs[1]
            cause_group['ClaimsID'] = claims_data_service.generate_alpha_id()
            filter_total_time = total_time_millis(filter_start_time)
            logger.info("Filter Relevant Claims Duration: " + str(filter_total_time) + "ms")

            if token.is_expired():
                token.refresh_openai_token(rag_settings=rag_settings)

            worker_task.update_task_status_in_progress(percent_complete=20,
                                                       status_desc="Claims Explorer task is refining results!")

            # Call GPT-3.5 to get the full list of possible claims, returning only the positive results
            relevant_claim_start_time = current_time_millis()
            relevant_claims = await self.get_claim_relevance_editorial(rag_settings=rag_settings,
                                                                       question=user_question,
                                                                       cause_group=cause_group,
                                                                       jurisdictions=remapped_jurisdictions,
                                                                       is_mock_request=is_mock_request,
                                                                       job_metrics=job_metrics)
            relevant_claim_total_time = total_time_millis(relevant_claim_start_time)
            logger.info("Get Relevant Claims Duration: " + str(relevant_claim_total_time) + "ms")

            # Restructure the positive claims into a dictionary
            positive_claims = await self.restructure_editorial_claims_matches(cause_group, remapped_jurisdictions)

            if token.is_expired():
                token.refresh_openai_token(rag_settings=rag_settings)

            worker_task.update_task_status_in_progress(percent_complete=60,
                                                       status_desc="Claims Explorer task is generating answers!")

            # Call GPT-4 to generate explanations for the positive claims and format the final output
            gen_explanation_start_time = current_time_millis()
            final_output_dict = await self.generate_claims_explanation(rag_settings=rag_settings,
                                                                       question=user_question,
                                                                       positive_claims=positive_claims,
                                                                       is_mock_request=is_mock_request,
                                                                       job_metrics=job_metrics)
            gen_explanation_total_time = total_time_millis(gen_explanation_start_time)
            logger.info("Generate Explanations Duration: " + str(gen_explanation_total_time) + "ms")

            final_editorial_dict = self.append_35_to_editorial_results(final_output=final_output_dict,
                                                                       three_five_results=relevant_claims[0])

            job_metrics.job_end_time = current_time_millis()

            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="SUCCESS")

            logger.info(f"{formatted_job_metric_string}")

        except Exception as e:
            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="FAILED",
                                                                        final_exception=e)
            logger.info(f"{formatted_job_metric_string}")
            logger.error(f"{OpenAIService.__name__}::{type(e).__name__} Claims Explorer Execution failed! {e}; "
                         f"{traceback.format_tb(e.__traceback__)} ")
            raise ClaimsExplorerException

        return final_editorial_dict

    async def do_ai_claims_exploration(self, rag_settings: ClaimExRAGSettings, conversation_id: str,
                                       conversation_entry_id: str, question: str, worker_task: WorkerTask,
                                       jurisdictions: list[str], is_mock_request: bool) -> ClaimsExplorerResult:

        job_metrics = OpenAIMetrics(conversation_id=conversation_id,
                                    conversation_entry_id=conversation_entry_id,
                                    jurisdictions=jurisdictions,
                                    job_start_time=current_time_millis())

        logger.info(f"==**== Claims Explorer Task Started ==**==\n"
                    f" Conversation ID: {conversation_id} - Conversation Entry ID: {conversation_entry_id}\n"
                    f" Jurisdictions: {jurisdictions}\n"
                    f" Input Question: {question}\n ")

        try:
            token.refresh_openai_token(rag_settings=rag_settings)

            remapped_jurisdictions = self.remap_jurisdictions(jurisdictions)

            worker_task.update_task_status_in_progress(percent_complete=5,
                                                       status_desc="Claims Explorer task is filtering results!")

            # Call GPT-4 to filter out irrelevant claims and reduce token usage for future calls
            filter_start_time = current_time_millis()
            filtered_claims = await self.filter_relevant_claims(rag_settings=rag_settings,
                                                                question=question,
                                                                jurisdictions=remapped_jurisdictions,
                                                                is_mock_request=is_mock_request,
                                                                job_metrics=job_metrics)
            filter_total_time = total_time_millis(filter_start_time)
            logger.info("Filter Relevant Claims Duration: " + str(filter_total_time) + "ms")

            if token.is_expired():
                token.refresh_openai_token(rag_settings=rag_settings)

            worker_task.update_task_status_in_progress(percent_complete=20,
                                                       status_desc="Claims Explorer task is refining results!")

            # Call GPT-3.5 to get the full list of possible claims, returning only the positive results
            relevant_claim_start_time = current_time_millis()
            relevant_claims = await self.get_relevant_claims(rag_settings=rag_settings,
                                                             question=question,
                                                             filtered_claims=filtered_claims,
                                                             jurisdictions=remapped_jurisdictions,
                                                             is_mock_request=is_mock_request,
                                                             job_metrics=job_metrics)
            relevant_claim_total_time = total_time_millis(relevant_claim_start_time)
            logger.info("Get Relevant Claims Duration: " + str(relevant_claim_total_time) + "ms")

            # Restructure the positive claims into a dictionary
            positive_claims = await self.restructure_positive_claims_matches(relevant_claims)

            if token.is_expired():
                token.refresh_openai_token(rag_settings=rag_settings)

            worker_task.update_task_status_in_progress(percent_complete=60,
                                                       status_desc="Claims Explorer task is generating answers!")

            # Generate explanations for the positive claims and format the final output
            gen_explanation_start_time = current_time_millis()
            final_output_dict = await self.generate_claims_explanation(rag_settings=rag_settings,
                                                                       question=question,
                                                                       positive_claims=positive_claims,
                                                                       is_mock_request=is_mock_request,
                                                                       job_metrics=job_metrics)
            gen_explanation_total_time = total_time_millis(gen_explanation_start_time)
            logger.info("Generate Explanations Duration: " + str(gen_explanation_total_time) + "ms")

            job_metrics.job_end_time = current_time_millis()

            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="SUCCESS")

            logger.info(f"{formatted_job_metric_string}")

        except Exception as e:
            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="FAILED",
                                                                        final_exception=e)
            logger.info(f"{formatted_job_metric_string}")
            logger.error(f"{OpenAIService.__name__}::{type(e).__name__} Claims Explorer Execution failed! {e}; "
                         f"{traceback.format_tb(e.__traceback__)} ")
            raise ClaimsExplorerException

        return final_output_dict

    async def filter_relevant_claims(self, rag_settings: ClaimExRAGSettings, question: str, jurisdictions: list[str],
                                     is_mock_request: bool, job_metrics: OpenAIMetrics) -> list[str]:
        claims_filters: list[str] = claims_data_service.get_distinct_minimum_facts_for_jurisdictions(
            jurisdictions=jurisdictions)
        split_options = []

        step_metric = StepMetric(step_name="Filter Relevant Claims - GPT-4", step_start_time=current_time_millis())
        step_exceptions = []

        for claims_filter in claims_filters:
            if claims_filter:
                for sub_claim_filter in claims_filter.lower().split(';'):
                    if sub_claim_filter.lstrip():
                        split_options.append(sub_claim_filter.lstrip())

        unique_min_facts = list(sorted(set(split_options)))
        split_unique_min_facts = self.split_list_to_sublists_with_max_size(unique_min_facts, 100)

        filter_choices: list[str] = []
        claims_filter_callbacks: list[OpenAICallbackHandler] = []
        for min_fact_sublist in split_unique_min_facts:
            filter_check_string = "\\n".join(min_fact_sublist)

            num_attempts = 0

            while num_attempts < 2:
                try:
                    openai_gpt4_32k_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_32K
                    openai_gpt4_32k_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_32K.CUSTOM_HEADERS.copy()
                    openai_gpt4_32k_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "60"  # Set timeout for LLMProxy
                    sys_message = SystemMessagePromptTemplate.from_template(const.GPT_FILTER_SYS_MSG_PROMPT)
                    human_message = HumanMessagePromptTemplate.from_template(const.GPT_FILTER_HUMAN_MSG_PROMPT)
                    filter_prompt = ChatPromptTemplate.from_messages([sys_message, human_message])
                    filter_model = AzureChatOpenAI(temperature=0.0,
                                                   model=openai_gpt4_32k_settings.MODEL_NAME,
                                                   openai_api_type="azure_ad",
                                                   azure_endpoint=openai_gpt4_32k_settings.API_BASE_URL,
                                                   openai_api_version=openai_gpt4_32k_settings.API_VERSION,
                                                   openai_api_key=token.token,
                                                   deployment_name=openai_gpt4_32k_settings.DEPLOYMENT_NAME,
                                                   request_timeout=60.0,
                                                   default_headers=openai_gpt4_32k_headers,
                                                   max_retries=2)
                    chain = filter_prompt | filter_model
                    with get_openai_callback() as cb:
                        filter_options = chain.invoke({"filter_input": filter_check_string, "question_input": question})
                        claims_filter_callbacks.append(cb)
                        if filter_options.content:
                            if is_mock_request:
                                filter_options_split = mock_randomize_categorization_options(filter_check_string)
                            else:
                                filter_options_split = filter_options.content.split(";")

                            filter_choices.extend(filter_options_split)
                        else:
                            raise OpenAIResponseValidationException("No content returned from GPT call")
                        logger.debug(f"Filtered Choices: {filter_choices}")

                        break
                except Exception as e:
                    logger.error(f"{OpenAIService.__name__}::{type(e).__name__} exception occurred {e}; "
                                 f"{traceback.format_tb(e.__traceback__)} ")
                    step_exceptions.append(e)
                    if num_attempts >= 1:
                        raise ClaimsExplorerException("Failed to get filtered claims from GPT-4!")
                    num_attempts += 1
                    filter_choices = []

        step_metric.aggregate_callback_results(claims_filter_callbacks)
        step_metric.aggregate_exceptions(step_exceptions)
        step_metric.step_end_time = current_time_millis()
        formatted_step_metrics = step_metric.formatted_step_metrics()
        logger.info(f"{formatted_step_metrics}")
        job_metrics.step_metrics.append(step_metric)

        return filter_choices

    async def get_relevant_claims(self, rag_settings: ClaimExRAGSettings, question: str, filtered_claims: list[str],
                                  jurisdictions: list[str], is_mock_request: bool,
                                  job_metrics: OpenAIMetrics) -> list[dict]:
        combined_claim_answer_inputs = []

        step_metric = StepMetric(step_name="Get Claims Answer - GPT-3.5", step_start_time=current_time_millis())

        for jurisdiction in jurisdictions:
            jurisdiction_statutes = claims_data_service.get_statutes_by_jurisdiction(filtered_claims, jurisdiction)
            texts = [{"question_input": question, "cause_list": cause_grouping} for cause_grouping in
                     self.cause_grouper(jurisdiction_statutes)]
            combined_claim_answer_inputs += texts

        sem = asyncio.Semaphore(GPT_3_5_MAX_CONCURRENCY)
        tasks = [
            self.run_ai_claims_answer(rag_settings=rag_settings,
                                      sem=sem,
                                      question=claim['question_input'],
                                      cause_grouping=claim['cause_list'],
                                      is_mock_request=is_mock_request)
            for claim in combined_claim_answer_inputs
        ]
        claims_answer_combined_output = await asyncio.gather(*tasks)

        formatted_system_outputs: list[dict] = []
        claims_answer_callback_outputs: list[OpenAICallbackHandler] = []
        step_exceptions: list[Exception] = []
        for claims_answer in claims_answer_combined_output:
            formatted_system_outputs.extend(claims_answer[0])
            claims_answer_callback_outputs.extend(claims_answer[1])
            step_exceptions.extend(claims_answer[2])

        step_metric.aggregate_callback_results(claims_answer_callback_outputs)
        step_metric.aggregate_exceptions(step_exceptions)
        step_metric.step_end_time = current_time_millis()
        formatted_step_metrics = step_metric.formatted_step_metrics()
        logger.info(f"{formatted_step_metrics}")
        job_metrics.step_metrics.append(step_metric)

        positive_claim_outputs: list[dict] = []
        for claim_output in formatted_system_outputs:
            try:
                if claim_output['Answer'].strip() != "No":
                    positive_claim_outputs.append(claim_output)
            except Exception as e:
                logger.error(f"{OpenAIService.__name__}::{type(e).__name__} exception occurred {e}; "
                             f"{traceback.format_tb(e.__traceback__)} ")
        return positive_claim_outputs

    async def get_claim_relevance_editorial(self, rag_settings: ClaimExRAGSettings, question: str,
                                            cause_group: dict[str], jurisdictions: list[str], is_mock_request: bool,
                                            job_metrics: OpenAIMetrics) -> list[dict]:
        combined_claim_answer_inputs = []

        step_metric = StepMetric(step_name="Get Claims Answer - GPT-3.5", step_start_time=current_time_millis())

        texts = [{"question_input": question, "cause_list": self.format_editorial_chunk(cause_group)}]

        combined_claim_answer_inputs += texts

        sem = asyncio.Semaphore(GPT_3_5_MAX_CONCURRENCY)
        tasks = [
            self.run_ai_claims_answer_editorial(rag_settings=rag_settings,
                                                sem=sem,
                                                question=claim['question_input'],
                                                cause_grouping=claim['cause_list'],
                                                is_mock_request=is_mock_request)
            for claim in combined_claim_answer_inputs
        ]
        claims_answer_combined_output = await asyncio.gather(*tasks)

        formatted_system_outputs: list[dict] = []
        claims_answer_callback_outputs: list[OpenAICallbackHandler] = []
        step_exceptions: list[Exception] = []
        for claims_answer in claims_answer_combined_output:
            formatted_system_outputs.extend(claims_answer[0])
            claims_answer_callback_outputs.extend(claims_answer[1])
            step_exceptions.extend(claims_answer[2])

        step_metric.aggregate_callback_results(claims_answer_callback_outputs)
        step_metric.aggregate_exceptions(step_exceptions)
        step_metric.step_end_time = current_time_millis()
        formatted_step_metrics = step_metric.formatted_step_metrics()
        logger.info(f"{formatted_step_metrics}")
        job_metrics.step_metrics.append(step_metric)

        positive_claim_outputs: list[dict] = []
        for claim_output in formatted_system_outputs:
            try:
                positive_claim_outputs.append(claim_output)
            except Exception as e:
                logger.error(f"{OpenAIService.__name__}::{type(e).__name__} exception occurred {e}; "
                             f"{traceback.format_tb(e.__traceback__)} ")
        return positive_claim_outputs

    async def run_ai_claims_answer(self, rag_settings: ClaimExRAGSettings, sem: Semaphore, question: str,
                                   cause_grouping: list[dict], is_mock_request: bool) -> (
            list[dict], list[OpenAICallbackHandler], list[Exception]):

        validated_response = False
        num_attempts = 0

        validated_responses: list[dict] = []
        callbacks: list[OpenAICallbackHandler] = []
        step_exceptions: list[Exception] = []

        async with sem:
            openai_gpt35_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_35TURBO
            openai_gpt35_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_32K.CUSTOM_HEADERS.copy()
            openai_gpt35_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "60"  # Set timeout for LLMProxy
            cause_grouping_str = self.format_grouped_chunks(cause_grouping)
            system_message = SystemMessagePromptTemplate.from_template(const.GPT_CLAIMS_ANSWER_SYS_MSG_PROMPT)
            claims_message = HumanMessagePromptTemplate.from_template(const.GPT_CLAIMS_ANSWER_HUMAN_MSG_PROMPT)
            final_prompt = ChatPromptTemplate.from_messages([system_message, claims_message])
            model = AzureChatOpenAI(temperature=0.0,
                                    model=openai_gpt35_settings.MODEL_NAME,
                                    openai_api_type="azure_ad",
                                    azure_endpoint=openai_gpt35_settings.API_BASE_URL,
                                    openai_api_version=openai_gpt35_settings.API_VERSION,
                                    openai_api_key=token.token,
                                    deployment_name=openai_gpt35_settings.DEPLOYMENT_NAME,
                                    request_timeout=60.0,
                                    default_headers=openai_gpt35_headers,
                                    max_tokens=200,
                                    stop=["Explanations:"])
            chain = final_prompt | model
            while num_attempts < 3 and not validated_response:
                try:
                    with get_openai_callback() as cb:
                        claims_answer_raw_output = await chain.ainvoke(
                            {"question_input": question, "cause_list": cause_grouping_str})
                    callbacks.append(cb)

                    if claims_answer_raw_output.content:
                        if is_mock_request:
                            formatted_responses = mock_randomize_filter_responses(cause_grouping)
                        else:
                            formatted_responses = self.format_gpt35_responses(
                                ai_response=claims_answer_raw_output.content)

                    if len(formatted_responses) == len(cause_grouping):
                        validated_response = True
                        validated_responses.extend(formatted_responses)
                    else:
                        num_attempts += 1
                        validated_responses = []
                except (InternalServerError, APITimeoutError) as network_error:
                    num_attempts += 1
                    validated_responses = []
                    # Have tried too many times, need to fail out
                    if num_attempts > 2:
                        logger.error(f"Repeated network errors while getting relevant claims!\n"
                                     f"{OpenAIService.__name__}::{type(network_error).__name__} exception occurred {network_error}; "
                                     f"{traceback.format_tb(network_error.__traceback__)} ")
                    else:
                        logger.error(f"Network error while getting relevant claims! Retrying...\n"
                                     f"{OpenAIService.__name__}::{type(network_error).__name__} exception occurred {network_error}; "
                                     f"{traceback.format_tb(network_error.__traceback__)} ")
                        raise network_error
                except RateLimitError as rle:
                    backoff_time = RATE_LIMIT_SLEEP * (num_attempts + 1)
                    logger.warn(f"Rate Limit Error occurred! Waiting {backoff_time} seconds before retrying!\n"
                                f"{OpenAIService.__name__}::{type(rle).__name__} exception occurred {rle}; "
                                f"{traceback.format_tb(rle.__traceback__)} ")
                    time.sleep(backoff_time)
                    num_attempts += 1
                    validated_responses = []

            if not validated_responses:
                step_exceptions.append(
                    OpenAIResponseValidationException(
                        f"Validation failed for cause grouping {cause_grouping}"))
                logger.warn(
                    f"{OpenAIService.__name__}::Validation repeatedly failed for cause grouping {cause_grouping}; "
                    f"attempting to run requests individually!")

                for cause in cause_grouping:
                    cause_grouping_str = self.format_grouped_chunks([cause])
                    try:
                        with get_openai_callback() as cb:
                            claims_answer_raw_output = await chain.ainvoke(
                                {"question_input": question, "cause_list": cause_grouping_str})
                        callbacks.append(cb)

                        if claims_answer_raw_output.content:
                            formatted_responses = self.format_gpt35_responses(
                                ai_response=claims_answer_raw_output.content)
                            if formatted_responses:
                                validated_responses.extend(formatted_responses)
                            else:
                                step_exceptions.append(OpenAIResponseValidationException(
                                    f"Validation failed for individual cause submission {cause}"))
                                logger.error(
                                    f"{OpenAIService.__name__}::Validation failed for individual cause submission {cause}; "
                                    f"skipping this cause!")
                        else:
                            step_exceptions.append(OpenAIResponseValidationException(
                                f"Validation failed for individual cause submission {cause}"))
                            logger.error(
                                f"{OpenAIService.__name__}::Validation failed for individual cause submission {cause}; "
                                f"skipping this cause!")
                    except (APITimeoutError, InternalServerError, RateLimitError) as rle:
                        step_exceptions.append(rle)
                        logger.error(f"OpenAI Error occurred! Skipping this individual cause {cause}!\n"
                                     f"{OpenAIService.__name__}::{type(rle).__name__} exception occurred {rle}; "
                                     f"{traceback.format_tb(rle.__traceback__)} ")

            return formatted_responses, callbacks, step_exceptions

    async def run_ai_claims_answer_editorial(self, rag_settings: ClaimExRAGSettings, sem: Semaphore, question: str,
                                             cause_grouping: str, is_mock_request: bool) -> (
            list[dict], list[OpenAICallbackHandler], list[Exception]):

        validated_response = False
        num_attempts = 0

        validated_responses: list[dict] = []
        callbacks: list[OpenAICallbackHandler] = []
        step_exceptions: list[Exception] = []

        async with sem:
            openai_gpt35_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_35TURBO
            openai_gpt35_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_32K.CUSTOM_HEADERS.copy()
            openai_gpt35_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "60"  # Set timeout for LLMProxy
            cause_grouping_str = cause_grouping
            system_message = SystemMessagePromptTemplate.from_template(const.GPT_CLAIMS_ANSWER_SYS_MSG_PROMPT)
            claims_message = HumanMessagePromptTemplate.from_template(const.GPT_CLAIMS_ANSWER_HUMAN_MSG_PROMPT)
            final_prompt = ChatPromptTemplate.from_messages([system_message, claims_message])
            model = AzureChatOpenAI(temperature=0.0,
                                    model=openai_gpt35_settings.MODEL_NAME,
                                    openai_api_type="azure_ad",
                                    azure_endpoint=openai_gpt35_settings.API_BASE_URL,
                                    openai_api_version=openai_gpt35_settings.API_VERSION,
                                    openai_api_key=token.token,
                                    deployment_name=openai_gpt35_settings.DEPLOYMENT_NAME,
                                    request_timeout=60.0,
                                    default_headers=openai_gpt35_headers,
                                    max_tokens=2000)
            chain = final_prompt | model
            while num_attempts < 3 and not validated_response:
                try:
                    with get_openai_callback() as cb:
                        claims_answer_raw_output = await chain.ainvoke(
                            {"question_input": question, "cause_list": cause_grouping_str})
                    callbacks.append(cb)

                    if claims_answer_raw_output.content:
                        formatted_responses = self.format_gpt35_editorial(
                            ai_response=claims_answer_raw_output.content)

                    if formatted_responses:
                        validated_response = True
                        validated_responses.extend(formatted_responses)
                    else:
                        num_attempts += 1
                        validated_responses = []
                except (InternalServerError, APITimeoutError) as network_error:
                    num_attempts += 1
                    validated_responses = []
                    # Have tried too many times, need to fail out
                    if num_attempts > 2:
                        logger.error(f"Repeated network errors while getting relevant claims!\n"
                                     f"{OpenAIService.__name__}::{type(network_error).__name__} exception occurred {network_error}; "
                                     f"{traceback.format_tb(network_error.__traceback__)} ")
                    else:
                        logger.error(f"Network error while getting relevant claims! Retrying...\n"
                                     f"{OpenAIService.__name__}::{type(network_error).__name__} exception occurred {network_error}; "
                                     f"{traceback.format_tb(network_error.__traceback__)} ")
                        raise network_error
                except RateLimitError as rle:
                    backoff_time = RATE_LIMIT_SLEEP * (num_attempts + 1)
                    logger.warn(f"Rate Limit Error occurred! Waiting {backoff_time} seconds before retrying!\n"
                                f"{OpenAIService.__name__}::{type(rle).__name__} exception occurred {rle}; "
                                f"{traceback.format_tb(rle.__traceback__)} ")
                    time.sleep(backoff_time)
                    num_attempts += 1
                    validated_responses = []

            return formatted_responses, callbacks, step_exceptions

    def format_gpt35_responses(self, ai_response: str) -> list[dict]:
        formatted_responses: list[dict] = []
        try:
            line_split_responses = list(filter(None, ai_response.splitlines()))
            for line_response in line_split_responses:
                if line_response:
                    if line_response.startswith("Output"):
                        continue
                    delimit_split_responses = line_response.split('|')
                    raw_claims_id = delimit_split_responses[0]
                    # GPT 3.5 is not the best at following instructions, so do a little bit of clean-up
                    claims_id = re.sub("ClaimsID=", "", raw_claims_id, flags=re.IGNORECASE)
                    claims_records = claims_data_service.get_records_by_claims_id(claims_id)

                    if len(claims_records) != 1:
                        break
                    claims_answer = delimit_split_responses[1]

                    formatted_responses.append({"ClaimsID": claims_id, "Answer": claims_answer})

        except Exception as e:
            logger.error(
                f"{OpenAIService.__name__}::{type(e).__name__} exception while formatting GPT 3.5 response: {e}; gpt response data: {ai_response}")

        return formatted_responses

    def format_gpt35_editorial(self, ai_response: str) -> list[dict]:
        formatted_responses: list[dict] = []
        try:
            line_response_pattern = r".*\|(Yes|No|Possibly).*$"
            line_split_responses = list(filter(None, ai_response.splitlines()))
            for line_response in line_split_responses:
                if line_response and re.match(line_response_pattern, line_response, re.IGNORECASE):
                    delimit_split_responses = line_response.split('|')
                    title = delimit_split_responses[0]
                    # claims_records = claims_data_service.get_records_by_claims_id(title)
                    #
                    # if len(claims_records) != 1:
                    #     break
                    claims_answer = delimit_split_responses[1]
                    explanation_split_response = ai_response.split('Explanations:\n')
                    explanation = '[[[GPT 3.5]]] ' + explanation_split_response[1]

                    formatted_responses.append({"Title": title, "Answer": claims_answer, "Explanation": explanation})
                    break

        except Exception as e:
            logger.error(
                f"{OpenAIService.__name__}::{type(e).__name__} exception while formatting GPT 3.5 response: {e}; gpt response data: {ai_response}")

        return formatted_responses

    async def restructure_positive_claims_matches(self, positive_claim_matches: list[dict]) -> dict:
        structured_dict = {}
        for positive_claim in positive_claim_matches:
            logger.debug(f"Positive Claim: {positive_claim}")
            claims_id = positive_claim['ClaimsID']
            matched_claims: list[dict] = claims_data_service.get_records_by_claims_id(claims_id)

            # Check if a result was found
            if len(matched_claims) == 1:
                matched_claim: dict = matched_claims[0]
                logger.debug(f"Claims String: {matched_claim}")

                # Add the description to the dictionary
                structured_dict[claims_id] = {"ClaimsID": "", "Title": "", "Citation": "",
                                              "Long Desc": "", "Short Desc": ""}
                structured_dict[claims_id]['ClaimsID'] = claims_id
                structured_dict[claims_id]['Title'] = matched_claim['StatuteTitle']
                structured_dict[claims_id]['Citation'] = matched_claim['subsection_cite']
                structured_dict[claims_id]['Long Desc'] = matched_claim['long_description']
                structured_dict[claims_id]['Short Desc'] = matched_claim['short_description']
            elif len(matched_claims) == 0:
                logger.error(f"No matching claims found for {claims_id} in claims data set")
            else:
                logger.error(f"Multiple claim matches found for ClaimsID: {claims_id}")
        return structured_dict

    async def restructure_editorial_claims_matches(self, editorial_claim: dict, jurisdictions: list[str]) -> dict:

        # Add the jurisdiction to the dictionary
        structured_dict = {'ClaimsID': editorial_claim['ClaimsID'], 'Title': editorial_claim['Title'],
                           'Citation': editorial_claim['Citation'], 'Long Desc': editorial_claim['LongDescription'],
                           'Short Desc': editorial_claim['ShortDescription'], 'Jurisdictions': jurisdictions,
                           'PopularNames': editorial_claim['PopularNames'], 'SourceLaw': editorial_claim['sourceLaw']}

        return structured_dict

    async def generate_claims_explanation(self, rag_settings: ClaimExRAGSettings, question: str, positive_claims: dict,
                                          is_mock_request: bool, job_metrics: OpenAIMetrics) -> ClaimsExplorerResult:

        claims_explorer_results: ClaimsExplorerResult = ClaimsExplorerResult()
        step_metric = StepMetric(step_name="Generate Claims Explanation - GPT-4", step_start_time=current_time_millis())

        claims_explanation_combined_results = await self.run_ai_claims_explanation_entry(
            rag_settings=rag_settings,
            question=question,
            positive_claims=positive_claims,
            is_mock_request=is_mock_request)

        claims_explanation_results: list[dict] = [cb[0] for cb in claims_explanation_combined_results]
        claims_explanation_callbacks: list[OpenAICallbackHandler] = [cb[1] for cb in
                                                                     claims_explanation_combined_results]
        step_exceptions: list[Exception] = list(filter(None, [cb[2] for cb in claims_explanation_combined_results]))

        output_claim_metrics: dict[str, int] = {}

        try:
            statute_dicts = []
            constit_dicts = []
            comlaw_dicts = []

            for claim_explanation in claims_explanation_results:
                answer = claim_explanation['answer'].strip()
                source_law = claim_explanation['source_law']
                jurisdictions = claim_explanation['jurisdictions']
                if claim_explanation['answer'].strip() != "No" or "-editorial" in rag_settings.tags[0].lower():
                    for juris in jurisdictions:
                        metric_key = f"{source_law} {juris} {answer}"
                        if metric_key not in output_claim_metrics:
                            output_claim_metrics[metric_key] = 1
                        else:
                            output_claim_metrics[metric_key] = output_claim_metrics[metric_key] + 1

                    if claim_explanation['source_law'] == "common law":
                        comlaw_dicts.append(claim_explanation)
                    elif claim_explanation['source_law'] == "constitutional":
                        constit_dicts.append(claim_explanation)
                    else:
                        statute_dicts.append(claim_explanation)

            roy_statute_dicts = self.populate_royalty_ids(rag_settings=rag_settings, claim_results=statute_dicts)
            roy_constit_dicts = self.populate_royalty_ids(rag_settings=rag_settings, claim_results=constit_dicts)
            roy_comlaw_dicts = self.populate_royalty_ids(rag_settings=rag_settings, claim_results=comlaw_dicts)

            sorted_statute_dicts = self.sort_for_output(source_law="statutes", docs=roy_statute_dicts,
                                                        is_mock_run=is_mock_request)
            sorted_constit_dicts = self.sort_for_output(source_law="constitutional", docs=roy_constit_dicts,
                                                        is_mock_run=is_mock_request)
            sorted_comlaw_dicts = self.sort_for_output(source_law="common_law", docs=roy_comlaw_dicts,
                                                       is_mock_run=is_mock_request)

            claims_explorer_results.statutory_claims = sorted_statute_dicts
            claims_explorer_results.common_law_claims = sorted_comlaw_dicts
            claims_explorer_results.constitutional_claims = sorted_constit_dicts

        except Exception as e:
            logger.error(
                f"{OpenAIService.__name__}::{type(e).__name__} exception occurred {e}; {traceback.format_tb(e.__traceback__)}")
            raise e

        logger.info(f"Final Results from completed question: {question} is {claims_explorer_results}")

        step_metric.aggregate_callback_results(claims_explanation_callbacks)
        step_metric.aggregate_exceptions(step_exceptions)
        step_metric.step_end_time = current_time_millis()
        formatted_step_metrics = step_metric.formatted_step_metrics()
        logger.info(f"{formatted_step_metrics}")
        job_metrics.claim_metrics = output_claim_metrics
        job_metrics.step_metrics.append(step_metric)

        return claims_explorer_results

    async def run_ai_claims_explanation_entry(self, rag_settings: ClaimExRAGSettings, question: str,
                                              positive_claims: dict, is_mock_request: bool):
        sem = asyncio.Semaphore(GPT_4_MAX_CONCURRENCY)
        if "-editorial" in rag_settings.tags[0].lower():
            tasks = [
                self.run_ai_claims_explanation_editorial(rag_settings=rag_settings,
                                                         sem=sem,
                                                         question=question,
                                                         cause_input=positive_claims,
                                                         is_mock_request=is_mock_request)
            ]
            claims_explanation_combined_results = await asyncio.gather(*tasks)
        else:
            tasks = [
                self.run_ai_claims_explanation(rag_settings=rag_settings,
                                               sem=sem,
                                               question=question,
                                               cause_input=str(positive_claims[claims_id]),
                                               is_mock_request=is_mock_request)
                for claims_id in positive_claims.keys()
            ]
            claims_explanation_combined_results = await asyncio.gather(*tasks)
        return claims_explanation_combined_results

    async def run_ai_claims_explanation(self, rag_settings: ClaimExRAGSettings, sem: Semaphore, question: str,
                                        cause_input: str, is_mock_request: bool) -> (
            dict, OpenAICallbackHandler, list[Exception]):
        async with sem:
            validated_response = False
            num_attempts = 0

            formatted_explanation: dict = {}
            step_exceptions = []

            while num_attempts < 2 and not validated_response:
                try:
                    explanation_input = {"cause_list": cause_input, "question_input": question}
                    openai_gpt4_32k_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_32K
                    openai_gpt4_32k_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_32K.CUSTOM_HEADERS.copy()
                    openai_gpt4_32k_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "45"  # Set timeout for LLMProxy
                    sys_message = SystemMessagePromptTemplate.from_template(
                        const.GPT_GENERATE_EXPLANATION_SYS_MSG_PROMPT)
                    human_message: HumanMessagePromptTemplate = HumanMessagePromptTemplate.from_template(
                        const.GPT_GENERATE_EXPLANATION_HUMAN_MSG_PROMPT)
                    final_prompt: ChatPromptTemplate = ChatPromptTemplate.from_messages([sys_message, human_message])
                    model: AzureChatOpenAI = AzureChatOpenAI(temperature=0.0,
                                                             model=openai_gpt4_32k_settings.DEPLOYMENT_NAME,
                                                             openai_api_type="azure_ad",
                                                             azure_endpoint=openai_gpt4_32k_settings.API_BASE_URL,
                                                             openai_api_version=openai_gpt4_32k_settings.API_VERSION,
                                                             openai_api_key=token.token,
                                                             deployment_name=openai_gpt4_32k_settings.DEPLOYMENT_NAME,
                                                             request_timeout=45.0,  # This has no effect with LLM Proxy
                                                             default_headers=openai_gpt4_32k_headers,
                                                             max_retries=2)
                    # LLM Proxy injects text into the output for mocked requests, so it is not well-formed JSON
                    # can't use the SimpleJsonOutputParser in those instances, but we ignore the output anyways
                    # if is_mock_request:
                    #     chain: RunnableSequence = final_prompt | model
                    # else:
                    chain: RunnableSerializable = final_prompt | model | SimpleJsonOutputParser()
                    with get_openai_callback() as cb:
                        output: dict = await chain.ainvoke(explanation_input)
                        if is_mock_request:
                            output = mock_randomize_answer_responses(cause_input)

                        formatted_explanation = self.format_validate_explanation(output)

                        if formatted_explanation:
                            validated_response = True
                except Exception as e:
                    step_exceptions.append(e)
                    logger.error(
                        f"{OpenAIService.__name__}::{type(e).__name__} exception occurred {e}; "
                        f"{traceback.format_tb(e.__traceback__)}")
                    validated_response = False
                    num_attempts += 1
                    formatted_explanation = {}

                if num_attempts > 2:
                    logger.error(f"{OpenAIService.__name__}::Explanation Generation failed for {cause_input}!")
                    raise ClaimsExplorerException(f"Explanation Generation failed!")

            return formatted_explanation, cb, step_exceptions

    async def run_ai_claims_explanation_editorial(self, rag_settings: ClaimExRAGSettings, sem: Semaphore, question: str,
                                                  cause_input: dict, is_mock_request: bool) -> (
            dict, OpenAICallbackHandler, list[Exception]):
        async with sem:
            validated_response = False
            num_attempts = 0

            formatted_explanation: dict = {}
            step_exceptions = []

            while num_attempts < 2 and not validated_response:
                try:
                    explanation_input = {"cause_list": str(cause_input), "question_input": question}
                    openai_gpt4_32k_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_32K
                    openai_gpt4_32k_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_32K.CUSTOM_HEADERS.copy()
                    openai_gpt4_32k_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "45"  # Set timeout for LLMProxy
                    sys_message = SystemMessagePromptTemplate.from_template(
                        const.GPT_GENERATE_EXPLANATION_SYS_MSG_PROMPT)
                    human_message: HumanMessagePromptTemplate = HumanMessagePromptTemplate.from_template(
                        const.GPT_GENERATE_EXPLANATION_HUMAN_MSG_PROMPT)
                    final_prompt: ChatPromptTemplate = ChatPromptTemplate.from_messages([sys_message, human_message])
                    model: AzureChatOpenAI = AzureChatOpenAI(temperature=0.0,
                                                             model=openai_gpt4_32k_settings.DEPLOYMENT_NAME,
                                                             openai_api_type="azure_ad",
                                                             azure_endpoint=openai_gpt4_32k_settings.API_BASE_URL,
                                                             openai_api_version=openai_gpt4_32k_settings.API_VERSION,
                                                             openai_api_key=token.token,
                                                             deployment_name=openai_gpt4_32k_settings.DEPLOYMENT_NAME,
                                                             request_timeout=45.0,  # This has no effect with LLM Proxy
                                                             default_headers=openai_gpt4_32k_headers,
                                                             max_retries=2)
                    # LLM Proxy injects text into the output for mocked requests, so it is not well-formed JSON
                    # can't use the SimpleJsonOutputParser in those instances, but we ignore the output anyways
                    # if is_mock_request:
                    #     chain: RunnableSequence = final_prompt | model
                    # else:
                    chain: RunnableSerializable = final_prompt | model | SimpleJsonOutputParser()
                    with get_openai_callback() as cb:
                        output: dict = await chain.ainvoke(explanation_input)

                        formatted_explanation = self.format_final_explanation_editorial(output, cause_input)

                        if formatted_explanation:
                            validated_response = True
                except Exception as e:
                    step_exceptions.append(e)
                    logger.error(
                        f"{OpenAIService.__name__}::{type(e).__name__} exception occurred {e}; "
                        f"{traceback.format_tb(e.__traceback__)}")
                    validated_response = False
                    num_attempts += 1
                    formatted_explanation = {}

                if num_attempts > 2:
                    logger.error(f"{OpenAIService.__name__}::Explanation Generation failed for {str(cause_input)}!")
                    raise ClaimsExplorerException(f"Explanation Generation failed!")

            return formatted_explanation, cb, step_exceptions

    def format_validate_explanation(self, raw_explanations: dict) -> dict:
        formatted_explanation: dict = {}
        try:
            # This should always be exactly one, if it's zero or more than one, throw an exception
            claims_id = raw_explanations['ClaimsID']
            claim_records: list[dict] = claims_data_service.get_records_by_claims_id(claims_id)

            if len(claim_records) == 1:
                formatted_explanation = self.format_final_explanation(final_answer=raw_explanations,
                                                                      claim_record=claim_records[0])
            else:
                claims_record_ids: list[str] = [claim_record['ClaimsID'] for claim_record in claim_records]
                raise OpenAIResponseValidationException(
                    f"Unexpected number of results retrieved for Claim ID: {claims_id}! "
                    f"Values retrieved: {claims_record_ids}")
        except Exception as e:
            logger.error(f"{OpenAIService.__name__}::{type(e).__name__} exception occurred while validating "
                         f"and formatting explanations: {e}; {traceback.format_tb(e.__traceback__)}")
        return formatted_explanation

    def format_final_explanation(self, final_answer: dict, claim_record: dict) -> dict:
        alpha_claim_id = self.sanitize_none(claim_record['ClaimsID'])
        source_guid = self.sanitize_none(claim_record['docGUID'])
        first_line_citation = self.sanitize_none(claim_record['1stLineCitation'])
        citation = self.sanitize_none(claim_record['subsection_cite'])
        title = self.sanitize_none(claim_record['StatuteTitle'])
        jurisdictions = [claim_record['Jurisdiction']]
        source_law = self.sanitize_none(claim_record['sourceLaw'])
        sub_sort_order = self.sanitize_none(claim_record['sub_sort_order'])
        display_name = self.sanitize_none(claim_record['display_name'])
        last_amended = self.sanitize_none(claim_record['last_amended_formatted'])
        actionable_under = self.sanitize_none(claim_record['actionable_under'])
        action_under_guid = self.sanitize_none(claim_record['actionable_under_guid'])
        answer = final_answer['Answer']
        discussion = final_answer['Discussion']

        if source_law.strip().lower() != "statutory":
            citation = title

        # Flip the ID back to the original Claims ID in the source data
        claim_id = claims_data_service.get_claims_id_from_alpha_id(alpha_claim_id)

        final_formatted_answer = {
            "claim_id": claim_id,
            "source_guid": source_guid,
            "royalty_id": "00000",
            "1st_line_citation": first_line_citation,
            "citation": citation,
            "title": title,
            "jurisdictions": jurisdictions,
            "sub_sort_order": sub_sort_order,
            "display_name": display_name,
            "last_amended": last_amended,
            "actionable_under": actionable_under,
            "action_under_guid": action_under_guid,
            "source_law": source_law,
            "answer": answer,
            "discussion": discussion
        }
        return final_formatted_answer

    def format_final_explanation_editorial(self, final_answer: dict, claim_record: dict) -> dict:
        claim_id = self.sanitize_none(claim_record['ClaimsID'])
        source_guid = self.sanitize_none("none")
        first_line_citation = self.sanitize_none(claim_record['Citation'])
        citation = self.sanitize_none(claim_record['Citation'])
        title = self.sanitize_none(claim_record['Title'])
        jurisdictions = claim_record['Jurisdictions']
        source_law = self.sanitize_none(claim_record['SourceLaw'])
        sub_sort_order = self.sanitize_none('1')
        answer = final_answer['Answer']
        discussion = '[[[GPT 4]]] ' + final_answer['Discussion']

        if source_law.strip().lower() != "statutory":
            citation = title

        final_formatted_answer = {
            "claim_id": claim_id,
            "source_guid": source_guid,
            "royalty_id": "00000",
            "1st_line_citation": first_line_citation,
            "citation": citation,
            "title": title,
            "jurisdictions": jurisdictions,
            "sub_sort_order": sub_sort_order,
            "source_law": source_law,
            "answer": answer,
            "discussion": discussion
        }
        return final_formatted_answer

    def populate_royalty_ids(self, rag_settings: ClaimExRAGSettings, claim_results: list[dict]) -> list[dict]:

        guid_list = list(filter(None, [claim_result['source_guid'] for claim_result in claim_results]))

        open_search_service = OpenSearch(secret_open_search=rag_settings.open_search_settings.SECRET_OPEN_SEARCH,
                                         aws_region=rag_settings.REGION)

        guid_royalty_map: dict = {}
        if guid_list:
            open_search_response: dict = open_search_service.execute_doc_guid_open_search(doc_guids=guid_list)

            for open_search_doc in open_search_response['docs']:
                doc_guid = open_search_doc['_id']
                if open_search_doc['found']:
                    doc_status = open_search_doc['_source']['doc_status']
                    royalty_id = open_search_doc['_source']['royalty_id']
                    if doc_status == "Live":
                        guid_royalty_map[doc_guid] = royalty_id

        for claim_result in claim_results:
            claim_guid = claim_result['source_guid']
            if claim_guid:
                if claim_guid in guid_royalty_map:
                    claim_result['royalty_id'] = guid_royalty_map[claim_guid]
            else:
                claim_result['royalty_id'] = "00000"

        return claim_results

    def sort_for_output(self, source_law: str, docs: list[dict], is_mock_run: bool) -> list[dict]:
        sorted_docs = do_sort(source_law=source_law, entries=docs, is_mock_run=is_mock_run)
        return sorted_docs

    def split_list_to_sublists_with_max_size(self, input_list: list, max_size: int) -> list[list]:
        chunked_lists = [input_list[i:i + max_size] for i in range(0, len(input_list), max_size)]
        return chunked_lists

    def sanitize_none(self, value: str) -> Optional[str]:
        if value:
            if value.lower() == "none":
                return None
            elif value.lower() == "na":
                return None
            else:
                return value
        else:
            return None

    def cause_grouper(self, cause_list: list[dict]) -> list[list[dict]]:
        grouped_chunks: list[dict] = []
        current_chunk: list[str] = []
        current_chunk_length = 0
        chunks: list[list[dict]] = []
        max_chunk_length = 3500
        for cause_entry in cause_list:
            formatted_entry = self.format_chunk(cause_entry)
            entry_length = len(formatted_entry)
            # Check if adding the current string to the current chunk exceeds the maximum length
            if current_chunk_length + entry_length <= max_chunk_length:
                current_chunk.append(formatted_entry)
                grouped_chunks.append(cause_entry)
                current_chunk_length += entry_length
            else:
                chunks.append(grouped_chunks)
                grouped_chunks = [cause_entry]
                current_chunk = [formatted_entry]
                current_chunk_length = entry_length

        if current_chunk:
            chunks.append(grouped_chunks)

        return chunks

    def format_chunk(self, chunk: dict) -> str:
        formatted_string: str = f"ClaimsID={chunk['ClaimsID']} | Title={chunk['Title']} | Citation={chunk['Citation']} | " \
                                f"Long Desc={chunk['Long Desc']} | Short Desc={chunk['Short Desc']}"
        return formatted_string

    def format_editorial_chunk(self, chunk: dict):
        formatted_string: str = f"Title={chunk['Title']} | Citation={chunk['Citation']} | " \
                                f"PopularNames={chunk['PopularNames']} | " \
                                f"LongDescription={chunk['LongDescription']} | " \
                                f"ShortDescription={chunk['ShortDescription']}"
        return formatted_string

    def format_grouped_chunks(self, chunks: list[dict]) -> str:
        formatted_chunks = [self.format_chunk(chunk) for chunk in chunks]
        return "||\\n".join(formatted_chunks) + "###\\n"

    def remap_jurisdictions(self, jurisdictions: Optional[list[str]]) -> list[str]:
        default_jurisdictions = ["Federal"]

        if jurisdictions:
            remapped_jurisdictions = []
            for jurisdiction in jurisdictions:
                if jurisdiction in const.JURISDICTION_MAPPINGS:
                    remapped_jurisdictions.append(const.JURISDICTION_MAPPINGS[jurisdiction])
                else:
                    logger.warning(f"Provided jurisdiction {jurisdiction} not found in mappings!")
            return remapped_jurisdictions
        else:
            return default_jurisdictions

    def append_35_to_editorial_results(self, final_output: ClaimsExplorerResult, three_five_results: dict):
        result = final_output
        if final_output.common_law_claims:
            three_five_out = self.add_fields_to_35_editorial(final_output.common_law_claims[0], three_five_results)
            result.common_law_claims.append(three_five_out)
        elif final_output.constitutional_claims:
            three_five_out = self.add_fields_to_35_editorial(final_output.constitutional_claims[0], three_five_results)
            result.constitutional_claims.append(three_five_out)
        elif final_output.statutory_claims:
            three_five_out = self.add_fields_to_35_editorial(final_output.statutory_claims[0], three_five_results)
            result.statutory_claims.append(three_five_out)
        return result

    def add_fields_to_35_editorial(self, ref_dict: dict, three_five_results: dict):
        final_three_five_dict = {
            "claim_id": ref_dict['claim_id'],
            "source_guid": ref_dict['source_guid'],
            "royalty_id": ref_dict['royalty_id'],
            "1st_line_citation": ref_dict['1st_line_citation'],
            "citation": ref_dict['citation'],
            "title": ref_dict['title'],
            "jurisdictions": ref_dict['jurisdictions'],
            "source_law": ref_dict['source_law'],
            "answer": three_five_results['Answer'],
            "discussion": three_five_results['Explanation']
        }
        return final_three_five_dict
