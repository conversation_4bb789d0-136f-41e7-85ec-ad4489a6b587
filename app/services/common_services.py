from conversation_core.shared.services.llm_profile_service import LlmProfileService
from gcs_utils.entitlement_client import EntitlementClient
from raslogger import LoggingFactory

logger = LoggingFactory.get_logger(__name__)

_entitlement_client = None
_llm_profile_service = None


def get_entitlement_client(gcs_url: str):
    global _entitlement_client
    if _entitlement_client is None:
        _entitlement_client = EntitlementClient(gcs_entitlement_path=gcs_url)

    return _entitlement_client


def get_llm_profile_service(settings):
    global _llm_profile_service
    if _llm_profile_service is None:
        llm_env = "prod" if settings.ENVIRONMENT == "prod" else "preprod"
        _llm_profile_service = LlmProfileService(ras_config_base_url=settings.RAS_CONFIG_BASE_URL,
                                                 gcs_url=settings.GCS_URL,
                                                 gcs_user_secret=settings.GCS_USER_SECRET,
                                                 llm_environment=llm_env,
                                                 entitlement_client=get_entitlement_client(settings.GCS_URL))
    return _llm_profile_service
