import os
import re
import asyncio
import sys
import traceback
from asyncio import Se<PERSON>phore
from app.config.settings import ClaimExRAGSettings, Settings
from config import settings

from langchain_openai.chat_models import AzureChatOpenAI
from langchain.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate
)
from langchain.callbacks import get_openai_callback, OpenAICallbackHandler
from typing import Optional
from langchain.output_parsers.json import SimpleJsonOutputParser
from langchain.schema.runnable import RunnableSerializable
from common_utils.time_utils import current_time_millis, total_time_millis
from raslogger import LoggingFactory
from app.model import constants as const
from app.model.claims_explorer_result import ClaimsExplorerResult
from app.services.claims_data_service import ClaimsDataService
from app.utils.token_util import OpenAIToken
from app.utils.open_search_util import OpenSearch
from app.utils.custom_exceptions import *
from app.utils.sorting.sort_util import do_sort
from app.utils.open_ai_metrics import OpenAIMetrics, StepMetric
from utils.common_util import (
    mock_randomize_categorization_options,
    mock_randomize_answer_responses_v2
)
from utils.editorial_util import split_json_question_editorial_claim, user_input_to_json_editorial
from conversation_core.shared.constants import Constants

from conversation_core.shared.worker.worker_task import WorkerTask
from datadog_utils.utils import ras_tracer

logger = LoggingFactory.get_logger(__name__)

claims_data_service = ClaimsDataService()
settings: Settings = settings.get_settings()

# if llm-proxy is enabled, no need to create the azure token. llm-proxy will handle it
api_token = "__none__" if settings.LLM_PROXY_ENABLED else ""
token_exp = sys.maxsize if settings.LLM_PROXY_ENABLED else 0

token = OpenAIToken(token=api_token, token_expiration_time=token_exp)

run_env = os.getenv("ENVIRONMENT", "local")

GPT_4o_MAX_CONCURRENCY = 20 if run_env != "local" else 16
CLAIMEX_MAX_RUNTIME = 15 if run_env != "local" else 30
RATE_LIMIT_SLEEP = 5

logger.info(f"Pod in environment {run_env} started with GPT 4o concurrency {GPT_4o_MAX_CONCURRENCY} "
            f"and max runtime {CLAIMEX_MAX_RUNTIME} minutes")


class OpenAIServiceV2:

    async def claims_explorer(self, rag_settings: ClaimExRAGSettings, conversation_id: str, conversation_entry_id: str,
                              question: str, worker_task: WorkerTask, jurisdictions: list[str],
                              is_mock_request: bool) -> ClaimsExplorerResult:

        try:
            if "-editorial" in rag_settings.tags[0].lower():
                claimex_task = asyncio.create_task(self.do_ai_claims_editorial(rag_settings=rag_settings,
                                                                               conversation_id=conversation_id,
                                                                               conversation_entry_id=conversation_entry_id,
                                                                               question=question,
                                                                               worker_task=worker_task,
                                                                               jurisdictions=jurisdictions,
                                                                               is_mock_request=is_mock_request))

                result = await asyncio.wait_for(claimex_task, CLAIMEX_MAX_RUNTIME * 60)
            else:
                claimex_task = asyncio.create_task(self.do_ai_claims_exploration(rag_settings=rag_settings,
                                                                                 conversation_id=conversation_id,
                                                                                 conversation_entry_id=conversation_entry_id,
                                                                                 question=question,
                                                                                 worker_task=worker_task,
                                                                                 jurisdictions=jurisdictions,
                                                                                 is_mock_request=is_mock_request))

                result = await asyncio.wait_for(claimex_task, CLAIMEX_MAX_RUNTIME * 60)
        except asyncio.TimeoutError:
            raise ClaimsExplorationTimeoutException(
                f"Claims Exploration task timed out after {CLAIMEX_MAX_RUNTIME} minutes!")

        return result

    async def do_ai_claims_editorial(self, rag_settings: ClaimExRAGSettings, conversation_id: str,
                                     conversation_entry_id: str, question: str, worker_task: WorkerTask,
                                     jurisdictions: list[str], is_mock_request: bool) -> ClaimsExplorerResult:
        job_metrics = OpenAIMetrics(conversation_id=conversation_id,
                                    conversation_entry_id=conversation_entry_id,
                                    jurisdictions=jurisdictions,
                                    job_start_time=current_time_millis())

        logger.info(f"==**== Claims Explorer Editorial Task Started ==**==\n"
                    f" Conversation ID: {conversation_id} - Conversation Entry ID: {conversation_entry_id}\n"
                    f" Jurisdictions: {jurisdictions}\n"
                    f" Input Question: {question}\n ")

        try:
            token.refresh_openai_token(rag_settings=rag_settings)

            remapped_jurisdictions = self.remap_jurisdictions(jurisdictions)

            worker_task.update_task_status_in_progress(percent_complete=5,
                                                       status_desc="Claims Explorer task is filtering results!")

            # Parse the user input for the claims information.
            filter_start_time = current_time_millis()
            inputs = split_json_question_editorial_claim(question)
            cause_group = user_input_to_json_editorial(inputs[0])
            user_question = inputs[1]
            cause_group['ClaimsID'] = claims_data_service.generate_alpha_id()
            filter_total_time = total_time_millis(filter_start_time)
            logger.debug("Filter Relevant Claims Duration: " + str(filter_total_time) + "ms")

            worker_task.update_task_status_in_progress(percent_complete=20,
                                                       status_desc="Claims Explorer task is refining results!")

            possible_claims: list[dict] = [({"ClaimsID": cause_group['ClaimsID'],
                                             "Title": cause_group['Title'],
                                             "Citation": cause_group['Citation'],
                                             "Long Desc": cause_group['LongDescription'],
                                             "Short Desc": cause_group['ShortDescription'],
                                             "Jurisdictions": remapped_jurisdictions,
                                             "SourceLaw": cause_group['sourceLaw']}
                                           )]

            if token.is_expired():
                token.refresh_openai_token(rag_settings=rag_settings)

            worker_task.update_task_status_in_progress(percent_complete=60,
                                                       status_desc="Claims Explorer task is generating answers!")

            # Call GPT-4o to generate explanations for the possible claims and format the final output
            gen_explanation_start_time = current_time_millis()
            final_output_dict = await self.generate_claims_explanation(rag_settings=rag_settings,
                                                                       question=user_question,
                                                                       possible_claims=possible_claims,
                                                                       is_mock_request=is_mock_request,
                                                                       job_metrics=job_metrics)
            gen_explanation_total_time = total_time_millis(gen_explanation_start_time)
            logger.debug("Generate Explanations Duration: " + str(gen_explanation_total_time) + "ms")

            job_metrics.job_end_time = current_time_millis()

            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="SUCCESS")

            logger.info(f"{formatted_job_metric_string}")

        except Exception as e:
            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="FAILED",
                                                                        final_exception=e)
            logger.info(f"{formatted_job_metric_string}")
            logger.error(f"{OpenAIServiceV2.__name__}::{type(e).__name__} Claims Explorer Execution failed! {e}; "
                         f"{traceback.format_tb(e.__traceback__)} ")
            raise ClaimsExplorerException

        return final_output_dict

    async def do_ai_claims_exploration(self, rag_settings: ClaimExRAGSettings, conversation_id: str,
                                       conversation_entry_id: str, question: str, worker_task: WorkerTask,
                                       jurisdictions: list[str], is_mock_request: bool) -> ClaimsExplorerResult:

        job_metrics = OpenAIMetrics(conversation_id=conversation_id,
                                    conversation_entry_id=conversation_entry_id,
                                    jurisdictions=jurisdictions,
                                    job_start_time=current_time_millis())

        logger.info(f"==**== Claims Explorer Task Started ==**==\n"
                    f" Conversation ID: {conversation_id} - Conversation Entry ID: {conversation_entry_id}\n"
                    f" Jurisdictions: {jurisdictions}\n"
                    f" Input Question: {question}\n ")

        try:
            token.refresh_openai_token(rag_settings=rag_settings)

            remapped_jurisdictions = self.remap_jurisdictions(jurisdictions)

            worker_task.update_task_status_in_progress(percent_complete=5,
                                                       status_desc="Claims Explorer task is filtering results!")

            # Call GPT-4 to filter out irrelevant claims and reduce token usage for future calls
            filter_start_time = current_time_millis()
            filtered_claims = await self.filter_relevant_claims(rag_settings=rag_settings,
                                                                question=question,
                                                                jurisdictions=remapped_jurisdictions,
                                                                is_mock_request=is_mock_request,
                                                                job_metrics=job_metrics)
            filter_total_time = total_time_millis(filter_start_time)
            logger.debug("Filter Relevant Claims Duration: " + str(filter_total_time) + "ms")

            if token.is_expired():
                token.refresh_openai_token(rag_settings=rag_settings)

            worker_task.update_task_status_in_progress(percent_complete=20,
                                                       status_desc="Claims Explorer task is refining results!")

            possible_claims: list[dict] = []
            for jurisdiction in remapped_jurisdictions:
                jurisdiction_claims = claims_data_service.get_statutes_by_jurisdiction(filtered_claims, jurisdiction)
                possible_claims += jurisdiction_claims

            # Call GPT-4o to generate explanations for the possible claims and format the final output
            gen_explanation_start_time = current_time_millis()
            final_output_dict = await self.generate_claims_explanation(rag_settings=rag_settings,
                                                                       question=question,
                                                                       possible_claims=possible_claims,
                                                                       is_mock_request=is_mock_request,
                                                                       job_metrics=job_metrics)
            gen_explanation_total_time = total_time_millis(gen_explanation_start_time)
            logger.debug("Generate Explanations Duration: " + str(gen_explanation_total_time) + "ms")

            job_metrics.job_end_time = current_time_millis()

            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="SUCCESS")

            logger.info(f"{formatted_job_metric_string}")

        except Exception as e:
            formatted_job_metric_string = job_metrics.formatted_metrics(input_question=question,
                                                                        end_state="FAILED",
                                                                        final_exception=e)
            logger.info(f"{formatted_job_metric_string}")
            logger.error(f"{OpenAIServiceV2.__name__}::{type(e).__name__} Claims Explorer Execution failed! {e}; "
                         f"{traceback.format_tb(e.__traceback__)} ")
            raise ClaimsExplorerException

        return final_output_dict

    async def filter_relevant_claims(self, rag_settings: ClaimExRAGSettings, question: str, jurisdictions: list[str],
                                     is_mock_request: bool, job_metrics: OpenAIMetrics) -> list[str]:
        claims_filters: list[str] = claims_data_service.get_distinct_minimum_facts_for_jurisdictions(
            jurisdictions=jurisdictions)
        split_options = []

        step_metric = StepMetric(step_name="Filter Relevant Claims - GPT-4", step_start_time=current_time_millis())
        step_exceptions = []

        for claims_filter in claims_filters:
            if claims_filter:
                for sub_claim_filter in claims_filter.lower().split(';'):
                    if sub_claim_filter.lstrip():
                        split_options.append(sub_claim_filter.lstrip())

        unique_min_facts = list(sorted(set(split_options)))
        split_unique_min_facts = self.split_list_to_sublists_with_max_size(unique_min_facts, 100)

        filter_choices: list[str] = []
        claims_filter_callbacks: list[OpenAICallbackHandler] = []
        
        # Process each sublist with a timeout to prevent overall task timeout
        for min_fact_sublist in split_unique_min_facts:
            filter_check_string = "\\n".join(min_fact_sublist)
            num_attempts = 0

            while num_attempts < 2:
                try:
                    # Set a shorter timeout for this specific operation
                    prompt_chain: RunnableSerializable = self.filter_claims_prompt_chain(rag_settings=rag_settings)
                    
                    # Create a task with a timeout for this specific operation
                    async def process_sublist():
                        with get_openai_callback() as cb:
                            filter_options = prompt_chain.invoke(
                                {"filter_input": filter_check_string, "question_input": question})
                            return filter_options, cb
                    
                    try:
                        # Set a timeout of 60 seconds for this specific operation
                        filter_options, cb = await asyncio.wait_for(process_sublist(), timeout=60)
                        claims_filter_callbacks.append(cb)
                        
                        if filter_options.content:
                            if is_mock_request:
                                filter_options_split = mock_randomize_categorization_options(filter_check_string)
                            else:
                                filter_options_split = filter_options.content.split(";")

                            filter_choices.extend(filter_options_split)
                        else:
                            raise OpenAIResponseValidationException("No content returned from GPT call")
                        logger.debug(f"Filtered Choices: {filter_choices}")
                        break
                        
                    except asyncio.TimeoutError:
                        logger.warning(f"Timeout processing sublist in filter_relevant_claims. Skipping to next sublist.")
                        step_exceptions.append(Exception("Operation timed out for sublist processing"))
                        break  # Skip to next sublist instead of retrying
                        
                except Exception as e:
                    logger.error(f"{OpenAIServiceV2.__name__}::{type(e).__name__} exception occurred {e}; "
                                 f"{traceback.format_tb(e.__traceback__)} ")
                    step_exceptions.append(e)
                    if num_attempts >= 1:
                        # Don't raise exception here, just log and continue with partial results
                        logger.error("Failed to get filtered claims from GPT-4 after retries. Continuing with partial results.")
                        break
                    num_attempts += 1
                    
        # Even if we have exceptions, try to proceed with whatever filter_choices we have
        step_metric.aggregate_callback_results(claims_filter_callbacks)
        step_metric.aggregate_exceptions(step_exceptions)
        step_metric.step_end_time = current_time_millis()
        formatted_step_metrics = step_metric.formatted_step_metrics()
        logger.debug(f"{formatted_step_metrics}")
        job_metrics.step_metrics.append(step_metric)

        # Return whatever we have, even if incomplete
        return filter_choices if filter_choices else []

    async def generate_claims_explanation(self, rag_settings: ClaimExRAGSettings, question: str,
                                          possible_claims: list[dict],
                                          is_mock_request: bool, job_metrics: OpenAIMetrics) -> ClaimsExplorerResult:

        claims_explorer_results: ClaimsExplorerResult = ClaimsExplorerResult()
        step_metric = StepMetric(step_name="Generate Claims Explanation - GPT-4o",
                                 step_start_time=current_time_millis())

        claims_explanation_combined_results = await self.run_ai_claims_explanation_entry(
            rag_settings=rag_settings,
            question=question,
            possible_claims=possible_claims,
            is_mock_request=is_mock_request)

        claims_explanation_results: list[list[dict]] = [cb[0] for cb in claims_explanation_combined_results]
        claims_explanation_callbacks: list[OpenAICallbackHandler] = [cb[1] for cb in
                                                                     claims_explanation_combined_results]
        step_exceptions: list[Exception] = list(filter(None, [cb[2] for cb in claims_explanation_combined_results]))

        output_claim_metrics: dict[str, int] = {}

        try:
            statute_dicts = []
            constit_dicts = []
            comlaw_dicts = []

            for claim_explanations in claims_explanation_results:
                for claim_explanation in claim_explanations:
                    answer = claim_explanation['answer'].strip()
                    source_law = claim_explanation['source_law']
                    jurisdictions = claim_explanation['jurisdictions']
                    if claim_explanation['answer'].strip() != "No" or "-editorial" in rag_settings.tags[0].lower():
                        for juris in jurisdictions:
                            metric_key = f"{source_law} {juris} {answer}"
                            if metric_key not in output_claim_metrics:
                                output_claim_metrics[metric_key] = 1
                            else:
                                output_claim_metrics[metric_key] = output_claim_metrics[metric_key] + 1

                        if claim_explanation['source_law'] == "common law":
                            comlaw_dicts.append(claim_explanation)
                        elif claim_explanation['source_law'] == "constitutional":
                            constit_dicts.append(claim_explanation)
                        else:
                            statute_dicts.append(claim_explanation)

            roy_statute_dicts = self.populate_royalty_ids(rag_settings=rag_settings, claim_results=statute_dicts)
            roy_constit_dicts = self.populate_royalty_ids(rag_settings=rag_settings, claim_results=constit_dicts)
            roy_comlaw_dicts = self.populate_royalty_ids(rag_settings=rag_settings, claim_results=comlaw_dicts)

            sorted_statute_dicts = self.sort_for_output(source_law="statutes", docs=roy_statute_dicts,
                                                        is_mock_run=is_mock_request)
            sorted_constit_dicts = self.sort_for_output(source_law="constitutional", docs=roy_constit_dicts,
                                                        is_mock_run=is_mock_request)
            sorted_comlaw_dicts = self.sort_for_output(source_law="common_law", docs=roy_comlaw_dicts,
                                                       is_mock_run=is_mock_request)

            claims_explorer_results.statutory_claims = sorted_statute_dicts
            claims_explorer_results.common_law_claims = sorted_comlaw_dicts
            claims_explorer_results.constitutional_claims = sorted_constit_dicts

        except Exception as e:
            logger.error(
                f"{OpenAIServiceV2.__name__}::{type(e).__name__} exception occurred {e}; {traceback.format_tb(e.__traceback__)}")
            raise e

        logger.debug(f"Final Results from completed question: {question} is {claims_explorer_results}")

        step_metric.aggregate_callback_results(claims_explanation_callbacks)
        step_metric.aggregate_exceptions(step_exceptions)
        step_metric.step_end_time = current_time_millis()
        formatted_step_metrics = step_metric.formatted_step_metrics()
        logger.debug(f"{formatted_step_metrics}")
        job_metrics.claim_metrics = output_claim_metrics
        job_metrics.step_metrics.append(step_metric)

        return claims_explorer_results

    @ras_tracer.wrap()
    async def run_ai_claims_explanation_entry(self, rag_settings: ClaimExRAGSettings, question: str,
                                              possible_claims: list[dict], is_mock_request: bool):
        sem = asyncio.Semaphore(GPT_4o_MAX_CONCURRENCY)

        grouped_claim_inputs: list[(str, dict)] = self.group_claims_for_answer_generation(
            possible_claims=possible_claims)

        prompt_chain: RunnableSerializable = self.generate_answer_prompt_chain(rag_settings=rag_settings)
        if "-editorial" in rag_settings.tags[0].lower():
            tasks = [
                self.run_ai_claims_explanation_editorial(prompt_chain=prompt_chain,
                                                         sem=sem,
                                                         question=question,
                                                         possible_claims_str=claim_grouping[0],
                                                         possible_claims_dicts=claim_grouping[1],
                                                         is_mock_request=is_mock_request)
                for claim_grouping in grouped_claim_inputs
            ]
            claims_explanation_combined_results = await asyncio.gather(*tasks)
        else:
            tasks = [
                self.run_ai_claims_explanation(prompt_chain=prompt_chain,
                                               sem=sem,
                                               question=question,
                                               possible_claims_str=claim_grouping[0],
                                               possible_claims_dicts=claim_grouping[1],
                                               is_mock_request=is_mock_request)
                for claim_grouping in grouped_claim_inputs
            ]
            claims_explanation_combined_results = await asyncio.gather(*tasks)
        return claims_explanation_combined_results

    async def run_ai_claims_explanation(self, prompt_chain: RunnableSerializable, sem: Semaphore,
                                        question: str, possible_claims_str: str,
                                        possible_claims_dicts: list[dict], is_mock_request: bool) -> (
            list[dict], OpenAICallbackHandler, list[Exception]):
        async with sem:
            validated_response = False
            num_attempts = 0

            formatted_explanations: list[dict] = []
            step_exceptions = []

            while num_attempts < 2 and not validated_response:
                try:
                    explanation_input = {"context": possible_claims_str, "user_query": question}
                    with get_openai_callback() as cb:
                        if is_mock_request:
                            explanation_outputs: list[dict] = mock_randomize_answer_responses_v2(
                                input_claims=possible_claims_dicts)
                        else:
                            raw_explanations: dict = await self.prompt_chain_invoke(prompt_chain, explanation_input)
                            explanation_outputs: list[dict] = self.validate_explanation_response(
                                input_claims=possible_claims_dicts,
                                raw_explanations=raw_explanations)

                        formatted_explanations = self.format_validate_explanations(
                            explanation_outputs=explanation_outputs)

                        if formatted_explanations:
                            validated_response = True
                except Exception as e:
                    step_exceptions.append(e)
                    logger.error(
                        f"{OpenAIServiceV2.__name__}::{type(e).__name__} exception occurred {e}; "
                        f"{traceback.format_tb(e.__traceback__)}")
                    validated_response = False
                    num_attempts += 1
                    formatted_explanations = []

                if num_attempts > 2:
                    logger.error(
                        f"{OpenAIServiceV2.__name__}::Explanation Generation failed for {possible_claims_str}!")
                    raise ClaimsExplorerException(f"Explanation Generation failed!")

            return formatted_explanations, cb, step_exceptions

    async def run_ai_claims_explanation_editorial(self, prompt_chain: RunnableSerializable, sem: Semaphore,
                                                  question: str, possible_claims_str: str,
                                                  possible_claims_dicts: list[dict], is_mock_request: bool) -> (
            dict, OpenAICallbackHandler, list[Exception]):
        async with sem:
            validated_response = False
            num_attempts = 0

            formatted_explanations: list[dict] = []
            step_exceptions = []

            while num_attempts < 2 and not validated_response:
                try:
                    explanation_input = {"context": possible_claims_str, "user_query": question}
                    with get_openai_callback() as cb:
                        raw_explanations: dict = await self.prompt_chain_invoke(prompt_chain, explanation_input)
                        explanation_outputs: list[dict] = self.validate_explanation_response(
                            input_claims=possible_claims_dicts,
                            raw_explanations=raw_explanations)

                        formatted_explanations = self.format_validate_explanations_editorial(
                            input_claims=possible_claims_dicts,
                            explanation_outputs=explanation_outputs)

                        if formatted_explanations:
                            validated_response = True
                except Exception as e:
                    step_exceptions.append(e)
                    logger.error(
                        f"{OpenAIServiceV2.__name__}::{type(e).__name__} exception occurred {e}; "
                        f"{traceback.format_tb(e.__traceback__)}")
                    validated_response = False
                    num_attempts += 1
                    formatted_explanations = []

                if num_attempts > 2:
                    logger.error(f"{OpenAIServiceV2.__name__}::Explanation Generation failed for {possible_claims_str}!")
                    raise ClaimsExplorerException(f"Explanation Generation failed!")

            return formatted_explanations, cb, step_exceptions

    def format_validate_explanations(self, explanation_outputs: list[dict]) -> list[dict]:
        formatted_explanations: list[dict] = []

        for explanation_output in explanation_outputs:
            try:
                # This should always be exactly one, if it's zero or more than one, throw an exception
                claims_id = explanation_output['ClaimsID']
                claim_records: list[dict] = claims_data_service.get_records_by_claims_id(claims_id)

                if len(claim_records) == 1:
                    formatted_explanation = self.format_final_explanation(final_answer=explanation_output,
                                                                          claim_record=claim_records[0])
                    formatted_explanations.append(formatted_explanation)
                else:
                    claims_record_ids: list[str] = [claim_record['ClaimsID'] for claim_record in claim_records]
                    raise OpenAIResponseValidationException(
                        f"Unexpected number of results retrieved for Claim ID: {claims_id}! "
                        f"Values retrieved: {claims_record_ids}")
            except Exception as e:
                logger.error(f"{OpenAIServiceV2.__name__}::{type(e).__name__} exception occurred while validating "
                             f"and formatting explanations: {e}; {traceback.format_tb(e.__traceback__)}")
        return formatted_explanations

    def format_validate_explanations_editorial(self, input_claims: list[dict],
                                               explanation_outputs: list[dict]) -> list[dict]:
        formatted_explanations: list[dict] = []

        explanation_output: dict = explanation_outputs[0]
        claim_record: dict = input_claims[0]

        try:
            formatted_explanation = self.format_final_explanation_editorial(final_answer=explanation_output,
                                                                            claim_record=claim_record)
            formatted_explanations.append(formatted_explanation)
        except Exception as e:
            logger.error(f"{OpenAIServiceV2.__name__}::{type(e).__name__} exception occurred while validating "
                         f"and formatting explanations: {e}; {traceback.format_tb(e.__traceback__)}")
        return formatted_explanations

    def format_final_explanation(self, final_answer: dict, claim_record: dict) -> dict:
        alpha_claim_id = self.sanitize_none(claim_record['ClaimsID'])
        source_guid = self.sanitize_none(claim_record['docGUID'])
        first_line_citation = self.sanitize_none(claim_record['1stLineCitation'])
        citation = self.sanitize_none(claim_record['subsection_cite'])
        title = self.sanitize_none(claim_record['StatuteTitle'])
        jurisdictions = [claim_record['Jurisdiction']]
        source_law = self.sanitize_none(claim_record['sourceLaw'])
        sub_sort_order = self.sanitize_none(claim_record['sub_sort_order'])
        display_name = self.sanitize_none(claim_record['display_name'])
        last_amended = self.sanitize_none(claim_record['last_amended_formatted'])
        actionable_under = self.sanitize_none(claim_record['actionable_under'])
        action_under_guid = self.sanitize_none(claim_record['actionable_under_guid'])
        answer = final_answer['answer']
        discussion = final_answer['thoughts']

        if source_law.strip().lower() != "statutory":
            citation = title

        # Flip the ID back to the original Claims ID in the source data
        claim_id = claims_data_service.get_claims_id_from_alpha_id(alpha_claim_id)

        final_formatted_answer = {
            "claim_id": claim_id,
            "source_guid": source_guid,
            "royalty_id": "00000",
            "1st_line_citation": first_line_citation,
            "citation": citation,
            "title": title,
            "jurisdictions": jurisdictions,
            "sub_sort_order": sub_sort_order,
            "display_name": display_name,
            "last_amended": last_amended,
            "actionable_under": actionable_under,
            "action_under_guid": action_under_guid,
            "source_law": source_law,
            "answer": answer,
            "discussion": discussion
        }
        return final_formatted_answer

    def format_final_explanation_editorial(self, final_answer: dict, claim_record: dict) -> dict:
        claim_id = self.sanitize_none(claim_record['ClaimsID'])
        source_guid = self.sanitize_none("none")
        first_line_citation = self.sanitize_none(claim_record['Citation'])
        citation = self.sanitize_none(claim_record['Citation'])
        title = self.sanitize_none(claim_record['Title'])
        jurisdictions = claim_record['Jurisdictions']
        source_law = self.sanitize_none(claim_record['SourceLaw'])
        sub_sort_order = self.sanitize_none('1')
        answer = final_answer['answer']
        discussion = '[[[GPT 4o]]] ' + final_answer['thoughts']

        if source_law.strip().lower() != "statutory":
            citation = title

        final_formatted_answer = {
            "claim_id": claim_id,
            "source_guid": source_guid,
            "royalty_id": "00000",
            "1st_line_citation": first_line_citation,
            "citation": citation,
            "title": title,
            "jurisdictions": jurisdictions,
            "sub_sort_order": sub_sort_order,
            "source_law": source_law,
            "answer": answer,
            "discussion": discussion
        }
        return final_formatted_answer

    def populate_royalty_ids(self, rag_settings: ClaimExRAGSettings, claim_results: list[dict]) -> list[dict]:

        guid_list = list(filter(None, [claim_result['source_guid'] for claim_result in claim_results]))

        open_search_service = OpenSearch(secret_open_search=rag_settings.open_search_settings.SECRET_OPEN_SEARCH,
                                         aws_region=rag_settings.REGION)

        guid_royalty_map: dict = {}
        if guid_list:
            open_search_response: dict = open_search_service.execute_doc_guid_open_search(doc_guids=guid_list)

            for open_search_doc in open_search_response['docs']:
                doc_guid = open_search_doc['_id']
                if open_search_doc['found']:
                    doc_status = open_search_doc['_source']['doc_status']
                    royalty_id = open_search_doc['_source']['royalty_id']
                    if doc_status == "Live":
                        guid_royalty_map[doc_guid] = royalty_id

        for claim_result in claim_results:
            claim_guid = claim_result['source_guid']
            if claim_guid:
                if claim_guid in guid_royalty_map:
                    claim_result['royalty_id'] = guid_royalty_map[claim_guid]
            else:
                claim_result['royalty_id'] = "00000"

        return claim_results

    def sort_for_output(self, source_law: str, docs: list[dict], is_mock_run: bool) -> list[dict]:
        sorted_docs = do_sort(source_law=source_law, entries=docs, is_mock_run=is_mock_run)
        return sorted_docs

    def split_list_to_sublists_with_max_size(self, input_list: list, max_size: int) -> list[list]:
        chunked_lists = [input_list[i:i + max_size] for i in range(0, len(input_list), max_size)]
        return chunked_lists

    def sanitize_none(self, value: str) -> Optional[str]:
        if value:
            if value.lower() == "none":
                return None
            elif value.lower() == "na":
                return None
            else:
                return value
        else:
            return None

    def group_claims_for_answer_generation(self, possible_claims: list[dict]) -> list[(str, dict)]:

        grouped_claims: list[(str, dict)] = []

        grouped_claim_chunks: str = ""
        grouped_claim_dicts: list[dict] = []

        max_chunk_length = 3500

        for possible_claim in possible_claims:
            formatted_chunk = self.format_chunk(possible_claim)
            entry_length = len(formatted_chunk)

            if len(grouped_claim_chunks) + entry_length <= max_chunk_length:
                grouped_claim_chunks += formatted_chunk
                grouped_claim_dicts.append(possible_claim)
            else:
                grouped_claims.append((grouped_claim_chunks, grouped_claim_dicts))
                grouped_claim_chunks = formatted_chunk
                grouped_claim_dicts = [possible_claim]

        if grouped_claim_chunks:
            grouped_claims.append((grouped_claim_chunks, grouped_claim_dicts))
        return grouped_claims

    def cause_grouper(self, cause_list: list[dict]) -> list[list[dict]]:
        grouped_chunks: list[dict] = []
        current_chunk: list[str] = []
        current_chunk_length = 0
        chunks: list[list[dict]] = []
        max_chunk_length = 3500
        for cause_entry in cause_list:
            formatted_entry = self.format_chunk(cause_entry)
            entry_length = len(formatted_entry)
            # Check if adding the current string to the current chunk exceeds the maximum length
            if current_chunk_length + entry_length <= max_chunk_length:
                current_chunk.append(formatted_entry)
                grouped_chunks.append(cause_entry)
                current_chunk_length += entry_length
            else:
                chunks.append(grouped_chunks)
                grouped_chunks = [cause_entry]
                current_chunk = [formatted_entry]
                current_chunk_length = entry_length

        if current_chunk:
            chunks.append(grouped_chunks)

        return chunks

    def format_chunk(self, chunk: dict) -> str:
        formatted_string: str = f"ClaimsID={chunk['ClaimsID']} | Title={chunk['Title']} | Citation={chunk['Citation']} | " \
                                f"Long Desc={chunk['Long Desc']} | Short Desc={chunk['Short Desc']}\n"
        return formatted_string

    def format_editorial_chunk(self, chunk: dict):
        formatted_string: str = f"Title={chunk['Title']} | Citation={chunk['Citation']} | " \
                                f"PopularNames={chunk['PopularNames']} | " \
                                f"LongDescription={chunk['LongDescription']} | " \
                                f"ShortDescription={chunk['ShortDescription']}"
        return formatted_string

    def format_grouped_chunks(self, chunks: list[dict]) -> str:
        formatted_chunks = [self.format_chunk(chunk) for chunk in chunks]
        return "||\\n".join(formatted_chunks) + "###\\n"

    def remap_jurisdictions(self, jurisdictions: Optional[list[str]]) -> list[str]:
        default_jurisdictions = ["Federal"]

        if jurisdictions:
            remapped_jurisdictions = []
            for jurisdiction in jurisdictions:
                if jurisdiction in const.JURISDICTION_MAPPINGS:
                    remapped_jurisdictions.append(const.JURISDICTION_MAPPINGS[jurisdiction])
                else:
                    logger.warning(f"Provided jurisdiction {jurisdiction} not found in mappings!")
            return remapped_jurisdictions
        else:
            return default_jurisdictions

    def validate_explanation_response(self, input_claims: list[dict], raw_explanations: dict) -> list[dict]:
        claims_responses: list[dict] = []
        claims_ids_validated: dict = {claim['ClaimsID']: False for claim in input_claims}

        if "output" in raw_explanations:
            explanation_outputs: list[dict] = raw_explanations["output"]

            for explanation_output in explanation_outputs:
                claims_id = explanation_output["ClaimsID"] if "ClaimsID" in explanation_output else None

                if not claims_id:
                    raise OpenAIResponseValidationException(f"No ClaimsID found in answer generation response!")

                if claims_id in claims_ids_validated.keys():
                    claims_ids_validated[claims_id] = True
                else:
                    raise OpenAIResponseValidationException(f"Unexpected ClaimsID {claims_id} returned in answer "
                                                            f"generation response! Expected one of {claims_ids_validated.keys()}")

                if "answer" in explanation_output:
                    explanation_output["answer"] = self.format_answer_response(explanation_output["answer"])
                else:
                    raise OpenAIResponseValidationException(
                        f"No answer found in answer generation response for claim with ClaimsID {claims_id}!")
                if "thoughts" not in explanation_output:
                    raise OpenAIResponseValidationException(
                        f"No thoughts key found in answer generation response for claim with ClaimsID {claims_id}!")

                claims_responses.append(explanation_output)
        else:
            raise OpenAIResponseValidationException(f"No output found in answer generation response from request "
                                                    f"with ClaimsIDs {claims_ids_validated.keys()}!")

        for claims_id, validated in claims_ids_validated.items():
            if not validated:
                raise OpenAIResponseValidationException(
                    f"ClaimsID {claims_id} not found in answer generation response!")

        return explanation_outputs

    def format_answer_response(self, answer: str) -> str:
        match answer.lower():
            case x if re.match(r"ye?s?", x):
                return "Yes"
            case x if re.match(r"no?", x):
                return "No"
            case x if re.match(r"possibly with additional facts?", x):
                return "Possibly with Additional Fact"
            case _:
                return answer

    def filter_claims_prompt_chain(self, rag_settings: ClaimExRAGSettings) -> RunnableSerializable:
        openai_gpt4o_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_4_1
        openai_gpt4o_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_4_1.CUSTOM_HEADERS.copy()
        openai_gpt4o_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "60"  # Set timeout for LLMProxy
        sys_message = SystemMessagePromptTemplate.from_template(const.GPT_FILTER_SYS_MSG_PROMPT)
        human_message = HumanMessagePromptTemplate.from_template(const.GPT_FILTER_HUMAN_MSG_PROMPT)
        filter_prompt = ChatPromptTemplate.from_messages([sys_message, human_message])
        filter_model = AzureChatOpenAI(temperature=0.0,
                                       model=openai_gpt4o_settings.MODEL_NAME,
                                       openai_api_type="azure_ad",
                                       azure_endpoint=openai_gpt4o_settings.API_BASE_URL,
                                       openai_api_version=openai_gpt4o_settings.API_VERSION,
                                       openai_api_key=token.token,
                                       deployment_name=openai_gpt4o_settings.DEPLOYMENT_NAME,
                                       request_timeout=60.0,
                                       default_headers=openai_gpt4o_headers,
                                       max_retries=2)
        prompt_chain: RunnableSerializable = filter_prompt | filter_model
        return prompt_chain

    def generate_answer_prompt_chain(self, rag_settings: ClaimExRAGSettings) -> RunnableSerializable:
        openai_gpt4o_settings = rag_settings.open_ai_settings.MODEL_SETTINGS_4_1
        openai_gpt4o_headers = rag_settings.open_ai_settings.MODEL_SETTINGS_4_1.CUSTOM_HEADERS.copy()
        openai_gpt4o_headers[Constants.LLM_PROMPT_TIMEOUT_HEADER] = "45"  # Set timeout for LLMProxy
        # sys_message = SystemMessagePromptTemplate.from_template(
        #     const.GPT_GENERATE_EXPLANATION_SYS_MSG_PROMPT_V7)
        human_message: HumanMessagePromptTemplate = HumanMessagePromptTemplate.from_template(
            const.GPT_GENERATE_EXPLANATION_HUMAN_MSG_PROMPT_V12_2)
        # final_prompt: ChatPromptTemplate = ChatPromptTemplate.from_messages([sys_message, human_message])
        final_prompt: ChatPromptTemplate = ChatPromptTemplate.from_messages([human_message])
        model: AzureChatOpenAI = AzureChatOpenAI(temperature=0.1,
                                                 model=openai_gpt4o_settings.DEPLOYMENT_NAME,
                                                 openai_api_type="azure_ad",
                                                 response_format={"type": "json_object"},
                                                 azure_endpoint=openai_gpt4o_settings.API_BASE_URL,
                                                 openai_api_version=openai_gpt4o_settings.API_VERSION,
                                                 openai_api_key=token.token,
                                                 deployment_name=openai_gpt4o_settings.DEPLOYMENT_NAME,
                                                 request_timeout=45.0,  # This has no effect with LLM Proxy
                                                 default_headers=openai_gpt4o_headers,
                                                 max_retries=2)

        prompt_chain: RunnableSerializable = final_prompt | model | SimpleJsonOutputParser()
        return prompt_chain

    @ras_tracer.wrap()
    async def prompt_chain_invoke(self, prompt_chain: RunnableSerializable, explanation_input):
        raw_explanations: dict = await prompt_chain.ainvoke(explanation_input)
        return raw_explanations
