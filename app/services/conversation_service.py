import asyncio
import functools
import traceback
import json
from asyncio import Future, Cancelled<PERSON>rror
from typing import Optional, List
from pydantic import BaseModel
from billiard.exceptions import SoftTimeLimitExceeded
from conversation_core.shared.constants import Constants
from conversation_core.shared.enums import RetrieveConversationEntryStatuses
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from conversation_core.shared.services.conversation_service_base import ConversationServiceBaseV3
from app.config.settings import (
    ClaimExRAGSettings,
    get_settings,
    map_to_intent_settings,
    map_to_claimex_settings
)
from common_utils.time_utils import current_time_millis, total_time_millis
from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.worker.worker_task import WorkerTask
from raslogger import LoggingFactory
from app.utils.exception_mappings import get_error_retryable, get_error_code, get_error_message
from app.services.email_service import EmailService
from app.utils.intent_resolver import check_intent
from app.services.openai_service import OpenAIService
from app.services.openai_service_v2 import OpenAIServiceV2
from model.claims_explorer_result import ClaimsExplorerR<PERSON>ult
from services.answer_profile_service import answer_profile_service
from conversation_core.shared.models.errors import ErrorModel
from conversation_core.shared.models.answer_profile import AnswerProfile
from utils.custom_exceptions import MaliciousContentException
from datadog_utils import metrics_client
from aalp_service.v2.intent_resolver.config import IntentResolverSettings

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
email_service = EmailService(ses_secret=settings.AWS_SES_SECRET)
openai_service = OpenAIService()
openai_service_v2 = OpenAIServiceV2()


class WestlawClaimsExplorerAnswer(BaseModel):
    system_output: ClaimsExplorerResult

    def get_results(self):
        results = {
            **self.system_output.model_dump(exclude_none=True, exclude_unset=True)
        }
        return results


class ConversationService(ConversationServiceBaseV3):
    def __init__(self):
        super().__init__(settings=settings,
                         dynamo_db_v2=dynamo_db_v2,
                         email_service=email_service,
                         answer_profile_service=answer_profile_service)
        logger.info("Initiated ConversationServiceBase V3")

    def get_error_details(self, ex: Exception) -> ErrorModel:
        error_model = ErrorModel()
        error_model.is_retryable = get_error_retryable(ex)
        error_model.code = get_error_code(ex)
        error_model.message = get_error_message(ex)
        return error_model

    def handle_error(
            self, user_id: str, conversation_id: str, conversation_entry_id: str, answer_profile: AnswerProfile,
            ex: Exception
    ):
        pass

    def finish_conversation(
            self,
            is_new_conversation: bool,
            user_id: str,
            user_input: str,
            answer_solution_profile: str,
            jurisdictions_override: Optional[List[str]],
            content_types_override: Optional[List[str]],
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            worker_task: WorkerTask,
            answer_profile: AnswerProfile,
            user_session: dict = None,
            meta_data: dict = None,
            results: any = None,
    ):
        pass

    async def generate_answer(
            self,
            is_new_conversation: bool,
            user_id: str,
            user_input: str,
            answer_solution_profile: str,
            jurisdictions_override: Optional[List[str]],
            content_types_override: Optional[List[str]],
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            answer_profile: AnswerProfile,
            tags: Optional[List[str]],
            worker_task: WorkerTask,
            user_session: dict = None,
            meta_data: dict = None,
            conversation_history: List[dict] = None,
    ) -> any:
        start_time = current_time_millis()
        rag_settings: ClaimExRAGSettings = map_to_claimex_settings(
            settings=settings,
            auth_token=auth_token,
            solution_profile=answer_profile.rag_solution,
            ras_profile=answer_solution_profile.lower(),
            answer_profile=answer_profile,
            conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id,
            cobalt_session=user_session,
            tags=tags,
            conversation_type=meta_data["conversation_type"],
            metadata=meta_data
        )
        jurisdictions = []
        try:
            jurisdictions = answer_profile.default_fermi_jurisdiction if jurisdictions_override is None else jurisdictions_override

            if meta_data.get("check_intent", True):
                content_types = content_types_override if content_types_override else answer_profile.default_content_types
                intent_settings: IntentResolverSettings = map_to_intent_settings(settings=settings,
                                                                                 auth_token=auth_token,
                                                                                 solution_profile=answer_profile.rag_solution,
                                                                                 answer_profile=answer_profile,
                                                                                 ras_profile=answer_solution_profile.lower(),
                                                                                 conversation_id=conversation_id,
                                                                                 conversation_entry_id=conversation_entry_id,
                                                                                 cobalt_session=user_session,
                                                                                 tags=tags,
                                                                                 metadata=meta_data)

                await check_intent(
                    profile=answer_profile.intent_profile,
                    message=user_input,
                    content_types=content_types,
                    fermi_jurisdictions=jurisdictions,
                    conversation_history=conversation_history,
                    intent_settings=intent_settings,
                    raise_exceptions=False
                )
            if "prompt injection test case" in user_input.lower():
                raise MaliciousContentException("Prompt injection test case")

            logger.info("start claims_explorer")
            metrics_client.send_metric(
                name="RagServiceInitializeDuration", value=total_time_millis(start_time), tags=tags
            )

            latency_st = current_time_millis()
            is_mock_request = True if "mock" in answer_profile.name.lower() else False
            worker_task.update_task_status_in_progress(percent_complete=1,
                                                       status_desc="Claims Explorer task running")

            loop = asyncio.get_event_loop()
            tasks = []
            try:
                if answer_profile.rag_solution.lower() == "wl-claims-explorer-rag-v01":
                    claimex_task = loop.create_task(
                        openai_service.claims_explorer(rag_settings=rag_settings,
                                                       conversation_id=conversation_id,
                                                       conversation_entry_id=conversation_entry_id,
                                                       question=user_input,
                                                       worker_task=worker_task,
                                                       is_mock_request=is_mock_request,
                                                       jurisdictions=jurisdictions)
                    )
                else:
                    claimex_task = loop.create_task(
                        openai_service_v2.claims_explorer(rag_settings=rag_settings,
                                                          conversation_id=conversation_id,
                                                          conversation_entry_id=conversation_entry_id,
                                                          question=user_input,
                                                          worker_task=worker_task,
                                                          is_mock_request=is_mock_request,
                                                          jurisdictions=jurisdictions)
                    )
                tasks.append(claimex_task)
                future_tasks: Future = asyncio.gather(*tasks)
                future_tasks.add_done_callback(functools.partial(self.on_task_done, user_id, conversation_id,
                                                                 conversation_entry_id, conversation_action_type))
                await future_tasks
            except (CancelledError, Exception) as ex:
                logger.error(
                    f"{WestlawClaimsExplorerAnswer.__name__}::{type(ex).__name__} exception occurred {ex} while "
                    f"executing async tasks; {traceback.format_tb(ex.__traceback__)}")
                for task in tasks:
                    if task and not task.cancelled():
                        task.cancel()
                raise ex
            logger.info(f"TIME TAKEN FOR CLAIM RESPONSE IS: {current_time_millis() - latency_st}")
            results: ClaimsExplorerResult = claimex_task.result()

            worker_task.update_task_status_in_progress(percent_complete=100,
                                                       status_desc="Claims Explorer task finished!")

            return WestlawClaimsExplorerAnswer(system_output=results.dict())
        except SoftTimeLimitExceeded as soft_timeout_ex:
            logger.warn(f"Soft timeout exceeded for conversation entry - {rag_settings.conversation_entry_id}.")
            raise soft_timeout_ex
        except CancelledError as cancel_error:
            dynamo_db_v2.update_conversation_entry(
                user_id=user_id,
                conversation_id=conversation_id,
                conversation_entry_id=conversation_entry_id,
                attribute_updates={Constants.CONV_STATUS: RetrieveConversationEntryStatuses.CANCELLED.value}
            )
            logger.info(f"Conversation Entry Status set to {RetrieveConversationEntryStatuses.CANCELLED.value}")
            raise cancel_error
        except Exception as ex:
            logger.error(f"Claims Explorer Operation failed! Error type: {type(ex).__name__}\n user_id: {user_id}\n"
                         f" conversation_id: {conversation_id}\n conversation_entry_id: {conversation_entry_id}\n"
                         f" jurisdictions: {jurisdictions}\n"
                         f" question: {user_input}\n conversation_start_time: {start_time}\n"
                         f" conversation_end_time: {current_time_millis()}\n")
            raise ex

    def get_intermediate_results_dict(self, results: any) -> dict:
        return results.get_results()

    def get_system_output(self, results: any, answer_profile: AnswerProfile) -> any:
        return results.get_results()

    def get_answer_text(self, results: any) -> str:
        return json.dumps(results.get_results())

    def get_user_input(self, results: any) -> str:
        return json.dumps(results.get_results())

    def get_conversation_history(self, is_new_conversation: bool, user_id: str, conversation_id: str):
        conversation = self.dynamo_db_v2.get_conversation(
            user_id=user_id, conversation_id=conversation_id, include_intermediate_results=True
        )
        return conversation.conversation_entries

    def on_task_done(self,
                     user_id: str,
                     conversation_id: str,
                     conversation_entry_id: str,
                     conversation_type: str,
                     _: Future
                     ):
        conversation_entry = self.dynamo_db_v2.get_conversation_entry(user_id=user_id,
                                                                      conversation_id=conversation_id,
                                                                      conversation_entry_id=conversation_entry_id)
        if conversation_entry.status == RetrieveConversationEntryStatuses.CANCELLING.value:
            logger.info("Celery worker task has been revoked, cancelling asyncio task(s)")
            raise CancelledError(f"Cancellation request issued by user for "
                                 f"conversation {conversation_id} {conversation_entry_id}")
        logger.info(
            f"{conversation_type} tasks for conversation {conversation_id} {conversation_entry_id} were finished")
