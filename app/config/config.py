# Copyright 2023 Thomson Reuters. All rights reserved.
# Proprietary and confidential information of Thomson Reuters. Disclosure,
# use, or reproduction without the written authorization of Thomson Reuters is prohibited.

"""Common Configuration Settings."""
from typing import Optional, Dict

from pydantic_settings import BaseSettings
from ras_events_client.models.event_models import Constants


class GCSSettings(BaseSettings):
    """GCS Configuration Settings."""

    URL_GCS: str = "https://entitlement-qa.gcs.int.thomsonreuters.com/v1/token"
    GCS_CREDENTIALS_SECRET: str = "a204383-labs-aalp-search-service-user"


class AWSSettings(BaseSettings):
    """AWS Configuration Settings."""

    REGION: str = "us-east-1"
    S3_BUCKET: str = "a204383-ml-workspace-aalpworkspacawxi-use1"
    read_timeout: int = 70
    retries: int = 3


class OpenSearchSettings(BaseSettings):
    """Open Search Configuration Settings."""

    SECRET_OPEN_SEARCH: str = "a204383-labs-aalp-opensearch-service"
    URL_OPEN_SEARCH: str = "https://ai-accelerate-open-search-qa-use1.4649.aws-int.thomsonreuters.com"


class CobaltSettings(BaseSettings):
    """Cobalt Configuration Settings."""

    URL_COBALT_SEARCH: str = "https://ai-acceleration-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com"
    ENDPOINT_COBALT_SEARCH_V3: str = "ai/v3/cobalt/search"
    COBALT_SESSION: dict = {
        Constants.SESSION_USER_SENSITIVITY: Constants.SESSION_STANDARD_SENSITIVITY,
        Constants.SESSION_USER_CLASSIFICATION: Constants.SESSION_DEFAULT_CLASSIFICATION,
    }
    ENDPOINT_COBALT_TREATISE_SEARCH_V1: str = "ai/v1/cobalt/treatisesearch"


class ModelSettings(BaseSettings):
    """Base Model Configuration Settings."""

    SECRET_AZURE_OPENAI: str
    API_BASE_URL: str
    API_VERSION: str
    MODEL_NAME: str
    DEPLOYMENT_NAME: str
    MODEL_TYPE: str
    USE_STUFF_PATTERN: bool
    TOKEN_LIMIT: int
    CUSTOM_HEADERS: dict


class OpenAISettings(BaseSettings):
    """OpenAI Configuration Settings."""

    REGION: str = "us-east-1"
    TIMEOUT: int = 600

    MODEL_SETTINGS_4o: ModelSettings = ModelSettings(
        SECRET_AZURE_OPENAI="a204383-aalp-azure-openai-serviceprincipal",
        API_BASE_URL="https://llm-proxy-qa.4649.aws-int.thomsonreuters.com/api/v1/proxy/azure",
        API_VERSION="2024-03-01-preview",
        MODEL_NAME="gpt-4o",
        DEPLOYMENT_NAME="gpt-4o",
        MODEL_TYPE="4o",
        USE_STUFF_PATTERN=True,
        TOKEN_LIMIT=27000,
        CUSTOM_HEADERS={},
    )

    MODEL_SETTINGS_o1: ModelSettings = ModelSettings(
        SECRET_AZURE_OPENAI="a204383-aalp-azure-openai-serviceprincipal",
        API_BASE_URL="https://llm-proxy-qa.4649.aws-int.thomsonreuters.com/api/v1/proxy/azure",
        API_VERSION="2024-03-01-preview",
        MODEL_NAME="gpt-o1",
        DEPLOYMENT_NAME="gpt-o1",
        MODEL_TYPE="o1-preview",
        USE_STUFF_PATTERN=True,
        TOKEN_LIMIT=27000,
        CUSTOM_HEADERS={},
    )

    MODEL_SETTINGS_4_1: ModelSettings = ModelSettings(
        SECRET_AZURE_OPENAI="a204383-aalp-azure-openai-serviceprincipal",
        API_BASE_URL="https://llm-proxy-qa.4649.aws-int.thomsonreuters.com/api/v1/proxy/azure",
        API_VERSION="2024-03-01-preview",
        MODEL_NAME="gpt-4.1",
        DEPLOYMENT_NAME="gpt-4.1",
        MODEL_TYPE="4.1",
        USE_STUFF_PATTERN=True,
        TOKEN_LIMIT=27000,
        CUSTOM_HEADERS={},
    )

    def return_model_settings(self, model: str):
        """Return the model settings based on the model name."""
        return {"4o": self.MODEL_SETTINGS_4o, "o1": self.MODEL_SETTINGS_o1,
                "4.1": self.MODEL_SETTINGS_4_1}[
            model.lower()
        ]


class BedrockModelSettings(BaseSettings):
    """Anthropic claude 2.1 with 200k token model"""

    API_BASE_URL: str = "https://llm-proxy-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com/api/v1/proxy/bedrock"
    MODEL_NAME: str
    TOKEN_LIMIT: int = 200000
    DEFAULT_MAX_TOKENS: int = 4096

    REGION: str = "us-east-1"
    TIMEOUT: int = 600

    CUSTOM_HEADERS: Dict[str, str] = {}
    DEPLOYMENT_NAME: Optional[str] = None
    SECRET_AZURE_OPENAI: Optional[str] = None


class LLMProviderSettings(BaseSettings):
    """LLM Provider Configuration Settings."""

    service_feature: Optional[str] = None
    user_id: Optional[str] = None


class AnthropicSettings(LLMProviderSettings):
    MODEL_SETTINGS_BEDROCK_3_HAIKU: BedrockModelSettings = BedrockModelSettings(
        MODEL_NAME="anthropic.claude-3-haiku-20240307-v1:0",
    )

    MODEL_SETTINGS_BEDROCK_3_SONNET: BedrockModelSettings = BedrockModelSettings(
        MODEL_NAME="anthropic.claude-3-sonnet-20240229-v1:0",
    )

    MODEL_SETTINGS_BEDROCK_3_OPUS: BedrockModelSettings = BedrockModelSettings(
        MODEL_NAME="anthropic.claude-3-opus-20240229-v1:0",
    )

    MODEL_SETTINGS_BEDROCK_3_5_SONNET: BedrockModelSettings = BedrockModelSettings(
        MODEL_NAME="anthropic.claude-3-5-sonnet-20240620-v1:0",
    )


class CommonSettings(BaseSettings):
    """Common Configuration Settings."""

    profile: Optional[str] = None

    auth_token: Optional[str] = None
    conversation_id: Optional[str] = None
    conversation_entry_id: Optional[str] = None

    query_max_char_limit: int = 2000
    query_min_char_limit: int = 7

    query_min_valid_tokens: int = 1
    query_min_valid_token_ratio: float = 0.2

    aws_settings: AWSSettings = AWSSettings()
    open_search_settings: OpenSearchSettings = OpenSearchSettings()
    cobalt_settings: CobaltSettings = CobaltSettings()
    open_ai_settings: OpenAISettings = OpenAISettings()

    # Default definition files are included in the project package.
    # A file name without a path will be looked for in aalp_service.v2.{service}.pipelines.
    # For local development, this value may be overridden with a fully qualified path to a custom definition file.
    PIPELINES_DEFINITION_FILE: str = ""
