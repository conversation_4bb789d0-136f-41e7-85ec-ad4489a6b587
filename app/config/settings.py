import json
import os
import traceback
from functools import lru_cache
from typing import Optional, List

from aalp_service.v2.intent_resolver.config import IntentResolverSettings
from configuration_utils.constants import Constants as SessionConstants
from conversation_core.shared.constants import Constants
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.profile_mapping_util import get_remapped_openai_profile, OverrideProfileMapping
from conversation_core.shared.services.llm_profile_service import LLMProfileEnvironment
from pydantic_settings import BaseSettings
from llm_proxy_client.data.llm_profiles import LlmProfileName
from raslogger.logging_factory import LoggingFactory

from config.config import (OpenAISettings, AWSSettings, OpenSearchSettings,
                           CobaltSettings, GCSSettings, AnthropicSettings)
from services.common_services import get_llm_profile_service
from services.helm_service import helm_service
from configuration_utils.configuration_core import CoreSettings, replace_region_values_in_settings

logger = LoggingFactory.get_logger(__name__)


class ClaimExRAGSettings(BaseSettings):
    auth_token: Optional[str] = None
    tags: Optional[List] = None
    profile: Optional[str] = None
    conversation_id: Optional[str] = None
    conversation_entry_id: Optional[str] = None

    gcs_settings: GCSSettings = GCSSettings()
    aws_settings: AWSSettings = AWSSettings()
    open_search_settings: OpenSearchSettings = OpenSearchSettings()
    cobalt_settings: CobaltSettings = CobaltSettings()
    open_ai_settings: OpenAISettings = OpenAISettings()
    anthropic_settings: AnthropicSettings = AnthropicSettings()

    OPEN_AI_SECRET: Optional[str] = None
    REGION: str = "us-east-1"


class Settings(CoreSettings):
    PROJECT_NAME: str = "Research Application Services (RAS) AI Acceleration"
    GCS_ENVIRONMENT: str
    patch_unittest: bool = False
    CLUSTER_SHORT: str

    SAGEMAKER_EMBEDDING_ENDPOINT: str
    OPEN_SEARCH_ENDPOINT: str
    OPEN_SEARCH_SECRET_ID: str
    OPEN_AI_SECRET: str

    COBALT_SEARCH_URL: str
    DYNAMO_TABLE_NAME: str
    LOGGER_FIX_LIST: str
    S3_BUCKET: str
    UDS_ENDPOINT: str
    RAG_SERVICE_TIMEOUT: str
    TEMP_FOLDER: str
    GCS_USER_SECRET: str
    GCS_URL: str
    AWS_SES_SECRET: str
    DEFAULT_PROFILE_MAPPING_SECRET: str
    DISABLE_ALL_PY_WARNINGS: Optional[bool] = True

    LLM_PROXY_ENABLED: Optional[bool] = False
    LLM_PROXY_BASE_URL: Optional[str] = ""
    LLM_PROXY_AZURE_URL_PATH: Optional[str] = ""
    LLM_PROXY_BEDROCK_URL_PATH: Optional[str] = ""


@lru_cache()
def get_settings():
    logger.info("current working directory: " + os.getcwd())
    logger.info("getting settings from file: " + ".env." + os.getenv("ENVIRONMENT", "local"))
    settings = Settings()
    updated_settings = replace_region_values_in_settings(settings)
    logger.info("settings are: " + json.dumps(settings.dict()))
    return updated_settings


def get_openai_secret_name_and_url(profile: str) -> OverrideProfileMapping:
    # This is a temporary configuration until ras config comes into place. This allows remapping to test other systems
    # without changing Westlaw since the profile is hard coded.
    return get_remapped_openai_profile(profile, get_settings().DEFAULT_PROFILE_MAPPING_SECRET)


def copy_model_settings(model_settings_to_copy: BaseSettings,
                        llm_type_header_append_str: str) -> BaseSettings:
    ret = model_settings_to_copy.model_copy(deep=True)
    if "CUSTOM_HEADERS" in ret and Constants.LLM_TYPE_HEADER in ret.CUSTOM_HEADERS:
        ret.CUSTOM_HEADERS[Constants.LLM_TYPE_HEADER] += llm_type_header_append_str
    return ret


def map_to_intent_settings(
        settings: Settings,
        auth_token: str,
        solution_profile: str,
        ras_profile: str,
        answer_profile: AnswerProfile,
        conversation_id: str,
        conversation_entry_id: str,
        cobalt_session: Optional[str] = None,
        tags: Optional[List[str]] = None,
        conversation_type: Optional[str] = Constants.CONV_INITIAL_CONVERSATION_NAME,
        metadata: Optional[dict] = None
) -> IntentResolverSettings:
    if metadata is None:
        metadata = {}
    intent_resolver_settings = IntentResolverSettings()
    try:
        intent_resolver_settings.query_min_char_limit = 1
        intent_resolver_settings.auth_token = auth_token
        intent_resolver_settings.tags = tags
        intent_resolver_settings.profile = solution_profile
        intent_resolver_settings.conversation_id = conversation_id
        intent_resolver_settings.conversation_entry_id = conversation_entry_id
        intent_resolver_settings.open_search_settings.SECRET_OPEN_SEARCH = settings.OPEN_SEARCH_SECRET_ID
        intent_resolver_settings.open_search_settings.URL_OPEN_SEARCH = settings.OPEN_SEARCH_ENDPOINT

        intent_resolver_settings.open_ai_settings.REGION = settings.REGION
        intent_resolver_settings.gcs_settings.URL_GCS = settings.GCS_URL
        intent_resolver_settings.cobalt_settings.URL_COBALT = settings.COBALT_SEARCH_URL
        intent_resolver_settings.cobalt_settings.COBALT_SESSION = cobalt_session if cobalt_session is not None else {}

        llm_proxy_env: LLMProfileEnvironment = LLMProfileEnvironment[
            (getattr(answer_profile, "llm_proxy_environment", LLMProfileEnvironment.PREPROD.value)).upper()]
        default_headers = {
            Constants.LLM_PROXY_API_KEY: "__none__",
            Constants.LLM_CHAT_PROFILE_NAME_HEADER: ras_profile,
            Constants.LLM_PROXY_AUTHORIZATION_HEADER: auth_token,
            Constants.LLM_CHILD_ENTRY_HEADER: f"conversation_entry_id={conversation_entry_id}",
            Constants.LLM_PARENT_ENTRY_HEADER: f"conversation_id={conversation_id}",
            Constants.LLM_TYPE_HEADER: conversation_type,
            Constants.LLM_MOCK_RESPONSE_HEADER: getattr(answer_profile, "llm_proxy_mock_enabled", False),
            Constants.LLM_MOCK_SLEEP_HEADER: getattr(answer_profile, "llm_proxy_mock_sleep_override", -1),
            Constants.GENERIC_USER_ID_HEADER: intent_resolver_settings.cobalt_settings.COBALT_SESSION.get(
                SessionConstants.SESSION_USER_GUID),
            Constants.GENERIC_SESSION_ID_HEADER: intent_resolver_settings.cobalt_settings.COBALT_SESSION.get(
                SessionConstants.SESSION_SESSION_ID),
            Constants.USER_CLASSIFICATION_HEADER: intent_resolver_settings.cobalt_settings.COBALT_SESSION.get(
                SessionConstants.SESSION_USER_CLASSIFICATION),
            Constants.USER_SENSITIVITY_HEADER: intent_resolver_settings.cobalt_settings.COBALT_SESSION.get(
                SessionConstants.SESSION_USER_SENSITIVITY),
            Constants.PRODUCT_VIEW: intent_resolver_settings.cobalt_settings.COBALT_SESSION.get(
                SessionConstants.SESSION_PRODUCT_VIEW),
            Constants.PRODUCT_NAME: intent_resolver_settings.cobalt_settings.COBALT_SESSION.get(
                SessionConstants.SESSION_PRODUCT_NAME),
            Constants.BLUE_GREEN_ROUTING_HEADER: "green" if metadata.get(Constants.TASK_MD_ROUTE_TO_GREEN,
                                                                         False) else "blue",
        }

        if helm_service.is_green:
            default_headers[Constants.BLUE_GREEN_ROUTING_HEADER] = "green"

        default_headers = {k: v for k, v in default_headers.items() if v is not None}
        llm_secrets = get_llm_profile_service(settings=settings).get_subscription_secret(subscription_name="default",
                                                                                         env=settings.ENVIRONMENT)
        azure_llm_url = f"{settings.LLM_PROXY_BASE_URL}{settings.LLM_PROXY_AZURE_URL_PATH}"

        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o.API_BASE_URL = azure_llm_url
        default_headers_4o = default_headers.copy()
        default_headers_4o[Constants.LLM_PROFILE_KEY_HEADER] = Constants.LLM_PROFILE_KEY_4o
        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o.CUSTOM_HEADERS = default_headers_4o
        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
            llm_proxy_env.value)

        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o = copy_model_settings(
            model_settings_to_copy=intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o,
            llm_type_header_append_str=f"_{Constants.CONV_INTENT_RESOLVER_NAME}"
        )
        intent_resolver_settings.jurisdiction_extractor_open_ai_settings.MODEL_SETTINGS_4o = copy_model_settings(
            model_settings_to_copy=intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o,
            llm_type_header_append_str="_jurisdiction_extractor")

        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER.API_BASE_URL = azure_llm_url
        default_headers_4o_nov = default_headers.copy()
        default_headers_4o_nov[Constants.LLM_PROFILE_KEY_HEADER] = "gpt-4o-2024-11-20"
        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER.CUSTOM_HEADERS = default_headers_4o_nov
        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
            llm_proxy_env.value)

        intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER = copy_model_settings(
            model_settings_to_copy=intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER,
            llm_type_header_append_str=f"_{Constants.CONV_INTENT_RESOLVER_NAME}"
        )
        intent_resolver_settings.jurisdiction_extractor_open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER = copy_model_settings(
            model_settings_to_copy=intent_resolver_settings.open_ai_settings.MODEL_SETTINGS_4o_NOVEMBER,
            llm_type_header_append_str="_jurisdiction_extractor")

        string_default_headers = {k: str(v) for k, v in default_headers.items()}
        bedrock_llm_url = f"{settings.LLM_PROXY_BASE_URL}{settings.LLM_PROXY_BEDROCK_URL_PATH}"

        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.API_BASE_URL = bedrock_llm_url
        default_headers_sonnet_3_5 = string_default_headers.copy()
        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.CUSTOM_HEADERS = default_headers_sonnet_3_5
        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.CUSTOM_HEADERS[
            Constants.LLM_PROFILE_KEY_HEADER] = "anthropic.claude-3-5-sonnet-20240620-v1:0"
        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
            llm_proxy_env.value)

        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET_V2.API_BASE_URL = bedrock_llm_url
        default_headers_sonnet_3_5_v2 = string_default_headers.copy()
        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET_V2.CUSTOM_HEADERS = default_headers_sonnet_3_5_v2
        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET_V2.CUSTOM_HEADERS[
            Constants.LLM_PROFILE_KEY_HEADER] = "anthropic.claude-3-5-sonnet-20241022-v2:0"
        intent_resolver_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET_V2.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
            llm_proxy_env.value)

    except Exception as e:
        logger.error(f"Error in map_to_intent_settings: {e}; Traceback: {traceback.print_tb(e.__traceback__)} ")
        raise e

    return intent_resolver_settings


def map_to_claimex_settings(
        settings: Settings,
        auth_token: str,
        solution_profile: str,
        ras_profile: str,
        answer_profile: AnswerProfile,
        conversation_id: str,
        conversation_entry_id: str,
        cobalt_session: Optional[str] = None,
        tags: Optional[List[str]] = None,
        conversation_type: Optional[str] = Constants.CONV_INITIAL_CONVERSATION_NAME,
        metadata: Optional[dict] = None
) -> ClaimExRAGSettings:
    if metadata is None:
        metadata = {}
    rag_settings = ClaimExRAGSettings()
    rag_settings.auth_token = auth_token
    rag_settings.tags = tags
    rag_settings.profile = solution_profile
    rag_settings.conversation_id = conversation_id
    rag_settings.conversation_entry_id = conversation_entry_id
    rag_settings.open_search_settings.SECRET_OPEN_SEARCH = settings.OPEN_SEARCH_SECRET_ID
    rag_settings.open_search_settings.URL_OPEN_SEARCH = settings.OPEN_SEARCH_ENDPOINT
    rag_settings.REGION = settings.REGION

    rag_settings.gcs_settings.URL_GCS = settings.GCS_URL
    rag_settings.cobalt_settings.URL_COBALT_SEARCH = settings.COBALT_SEARCH_URL
    rag_settings.cobalt_settings.COBALT_SESSION = cobalt_session if cobalt_session is not None else {}

    llm_proxy_env: LLMProfileEnvironment = LLMProfileEnvironment[
        (getattr(answer_profile, "llm_proxy_environment", LLMProfileEnvironment.PREPROD.value)).upper()]
    default_headers = {
        Constants.LLM_PROXY_API_KEY: "__none__",
        Constants.LLM_CHAT_PROFILE_NAME_HEADER: ras_profile,
        Constants.LLM_PROXY_AUTHORIZATION_HEADER: auth_token,
        Constants.LLM_CHILD_ENTRY_HEADER: f"conversation_entry_id={conversation_entry_id}",
        Constants.LLM_PARENT_ENTRY_HEADER: f"conversation_id={conversation_id}",
        Constants.LLM_TYPE_HEADER: conversation_type,
        Constants.LLM_MOCK_RESPONSE_HEADER: getattr(answer_profile, "llm_proxy_mock_enabled", False),
        Constants.LLM_MOCK_SLEEP_HEADER: getattr(answer_profile, "llm_proxy_mock_sleep_override", -1),
        Constants.GENERIC_USER_ID_HEADER: rag_settings.cobalt_settings.COBALT_SESSION.get(
            SessionConstants.SESSION_USER_GUID),
        Constants.GENERIC_SESSION_ID_HEADER: rag_settings.cobalt_settings.COBALT_SESSION.get(
            SessionConstants.SESSION_SESSION_ID),
        Constants.USER_CLASSIFICATION_HEADER: rag_settings.cobalt_settings.COBALT_SESSION.get(
            SessionConstants.SESSION_USER_CLASSIFICATION),
        Constants.USER_SENSITIVITY_HEADER: rag_settings.cobalt_settings.COBALT_SESSION.get(
            SessionConstants.SESSION_USER_SENSITIVITY),
        Constants.PRODUCT_VIEW: rag_settings.cobalt_settings.COBALT_SESSION.get(
            SessionConstants.SESSION_PRODUCT_VIEW),
        Constants.PRODUCT_NAME: rag_settings.cobalt_settings.COBALT_SESSION.get(
            SessionConstants.SESSION_PRODUCT_NAME),
        Constants.BLUE_GREEN_ROUTING_HEADER: "green" if metadata.get(Constants.TASK_MD_ROUTE_TO_GREEN,
                                                                     False) else "blue",
    }

    if helm_service.is_green:
        default_headers[Constants.BLUE_GREEN_ROUTING_HEADER] = "green"

    default_headers = {k: v for k, v in default_headers.items() if v is not None}
    llm_secrets = get_llm_profile_service(settings=settings).get_subscription_secret(subscription_name="default",
                                                                                     env=settings.ENVIRONMENT)
    azure_llm_url = f"{settings.LLM_PROXY_BASE_URL}{settings.LLM_PROXY_AZURE_URL_PATH}"

    rag_settings.open_ai_settings.MODEL_SETTINGS_4o.API_BASE_URL = azure_llm_url
    default_headers_4o = default_headers.copy()
    default_headers_4o[Constants.LLM_PROFILE_KEY_HEADER] = Constants.LLM_PROFILE_KEY_4o
    rag_settings.open_ai_settings.MODEL_SETTINGS_4o.CUSTOM_HEADERS = default_headers_4o
    rag_settings.open_ai_settings.MODEL_SETTINGS_4o.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
        llm_proxy_env.value)

    rag_settings.open_ai_settings.MODEL_SETTINGS_4_1.API_BASE_URL = azure_llm_url
    default_headers_4_1 = default_headers.copy()
    default_headers_4_1[Constants.LLM_PROFILE_KEY_HEADER] = LlmProfileName.GPT_4_1.value[0]
    rag_settings.open_ai_settings.MODEL_SETTINGS_4_1.CUSTOM_HEADERS = default_headers_4_1
    rag_settings.open_ai_settings.MODEL_SETTINGS_4_1.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
        llm_proxy_env.value)

    string_default_headers = {k: str(v) for k, v in default_headers.items()}
    bedrock_llm_url = f"{settings.LLM_PROXY_BASE_URL}{settings.LLM_PROXY_BEDROCK_URL_PATH}"

    rag_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.API_BASE_URL = bedrock_llm_url
    rag_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.CUSTOM_HEADERS = string_default_headers.copy()
    rag_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.CUSTOM_HEADERS[
        Constants.LLM_PROFILE_KEY_HEADER] = "anthropic.claude-3-5-sonnet-20240620-v1:0"
    rag_settings.anthropic_settings.MODEL_SETTINGS_BEDROCK_3_5_SONNET.SECRET_AZURE_OPENAI = llm_secrets.get_secret_name(
        llm_proxy_env.value)

    return rag_settings
