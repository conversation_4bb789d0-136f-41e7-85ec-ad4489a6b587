ENVIRONMENT=ci
GCS_ENVIRONMENT=qa
SAGEMAKER_EMBEDDING_ENDPOINT=a207891-ai-acceleration-coco-fips-qa-{region_short}
OPEN_SEARCH_ENDPOINT=https://vpc-a207891-ai-accel-qa-{region_short}-mkdhoyw3prstcsctoo6nwvgmcq.{region_long}.es.amazonaws.com
OPEN_SEARCH_SECRET_ID=a207891-ai-accel-qa-{region_short}-masterUserPassphrase
DYNAMO_TABLE_NAME=a207891-ai-acceleration-conversations-ci
OPEN_AI_SECRET=a207891/ras-search/ai-acceleration/ci/open-ai-secret
# Currently, we are providing the base path for the Cobalt service to the Lab service. However, the Lab service currently lacks the necessary configuration for this integration.
COBALT_SEARCH_URL="http://ai-acceleration-infra-service-ci.207891-ras-search-ai-ci.svc.cluster.local"
LOGGER_FIX_LIST="gunicorn,uvicorn,langchain.chat_models.openai,ddtrace.profiling,celery,flower,kombu,redis,asyncio,boto,kms,amazon,azure"
S3_BUCKET=a207891-ai-acceleration-conversations-ci-{region_short}
UDS_ENDPOINT=http://uds.int.next.qed.westlaw.com
EVENT_RECEIVER_ENDPOINT=https://event-receiver-ci.plexus-{cluster_short}-pp{region_short}.5771.aws-int.thomsonreuters.com/events/v2/event
DYNAMODB_TTL=7
DATADOG_HOST=https://app.ddog-gov.com
DATADOG_API_SECRET_NAME=a207891/ras-search/ai-acceleration/ci/ddog-gov-api-secret
DATADOG_METRIC_PREFIX=ras.search.conversations
OVERRIDE_HOST_NAME=local_developer
DATADOG_METRICS_ENABLED=False
EVENTS_ENABLED=False
RAG_SERVICE_TIMEOUT=540
TEMP_FOLDER=/temp
GCS_USER_SECRET=a207891/ras-search/ai-acceleration/ci/gcs-client-worker-secret
GCS_URL=https://entitlement-qa.gcs.int.thomsonreuters.com/v1/token
AWS_SES_SECRET=a207891/ras-search/ai-acceleration/ci/ses-credentials
DEFAULT_PROFILE_MAPPING_SECRET=a207891/ras-search/ai-acceleration/qa/profile-override-mappings
RAS_CONFIG_BASE_URL=http://ras-configuration-py-infra-service-qa.207891-ras-search-ai-qa.svc.cluster.local
LLM_PROXY_BASE_URL=http://llm-proxy-infra-service-ci.207891-ras-search-ai-ci.svc.cluster.local
LLM_PROXY_AZURE_URL_PATH=/api/v1/proxy/azure
LLM_PROXY_BEDROCK_URL_PATH=/api/v1/proxy/bedrock
LLM_PROXY_ENABLED=True
CLUSTER_SHORT={cluster_short}