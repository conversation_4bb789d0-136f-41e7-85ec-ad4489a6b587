ENVIRONMENT=prod
GCS_ENVIRONMENT=prod
SAGEMAKER_EMBEDDING_ENDPOINT=a207891-ai-acceleration-coco-fips-prod-{region_short}
OPEN_SEARCH_ENDPOINT=https://vpc-a207891-ai-accel-prod-{region_short}-qethrco3dj57zgtb7kp74znaiu.{region_long}.es.amazonaws.com
OPEN_SEARCH_SECRET_ID=a207891-ai-accel-prod-{region_short}-masterUserPassphrase
DYNAMO_TABLE_NAME=a207891-ai-acceleration-conversations-prod
OPEN_AI_SECRET=a207891/ras-search/ai-acceleration/prod/open-ai-secret
# Currently, we are providing the base path for the Cobalt service to the Lab service. However, the Lab service currently lacks the necessary configuration for this integration.
COBALT_SEARCH_URL="http://ai-acceleration-infra-service-prod.207891-ras-search-ai-prod.svc.cluster.local"
LOGGER_FIX_LIST="gunicorn,uvicorn,langchain.chat_models.openai,ddtrace.profiling,celery,flower,kombu,redis,asyncio,boto,kms,amazon,azure"
S3_BUCKET=a207891-ai-acceleration-conversations-prod-{region_short}
UDS_ENDPOINT=http://uds.int.next.westlaw.com
EVENT_RECEIVER_ENDPOINT=http://event-receiver-infra-service-prod.208203-events-prod.svc.cluster.local/events/v2/event
DYNAMODB_TTL=365
DATADOG_HOST=https://app.ddog-gov.com
DATADOG_API_SECRET_NAME=a207891/ras-search/ai-acceleration/prod/ddog-gov-api-secret
DATADOG_METRIC_PREFIX=ras.search.conversations
DATADOG_METRICS_ENABLED=True
EVENTS_ENABLED=True
RAG_SERVICE_TIMEOUT=540
TEMP_FOLDER=/tmp
GCS_USER_SECRET=a207891/ras-search/ai-acceleration/prod/gcs-client-worker-secret
GCS_URL=https://entitlement.gcs.thomsonreuters.com/v1/token
AWS_SES_SECRET=a207891/ras-search/ai-acceleration/prod/ses-credentials
DEFAULT_PROFILE_MAPPING_SECRET=a207891/ras-search/ai-acceleration/prod/profile-override-mappings
RAS_CONFIG_BASE_URL=http://ras-configuration-py-infra-service-prod.207891-ras-search-ai-prod.svc.cluster.local
LLM_PROXY_BASE_URL=http://llm-proxy-infra-service-prod.207891-ras-search-ai-prod.svc.cluster.local
LLM_PROXY_AZURE_URL_PATH=/api/v1/proxy/azure
LLM_PROXY_BEDROCK_URL_PATH=/api/v1/proxy/bedrock
LLM_PROXY_ENABLED=True
CLUSTER_SHORT={cluster_short}