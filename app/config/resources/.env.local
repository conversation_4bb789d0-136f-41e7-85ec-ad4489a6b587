ENVIRONMENT=local
GCS_ENVIRONMENT=qa
SAGEMAKER_EMBEDDING_ENDPOINT=a207891-ai-acceleration-coco-fips-qa-use1
OPEN_SEARCH_ENDPOINT=https://vpc-a207891-ai-accel-qa-use1-mkdhoyw3prstcsctoo6nwvgmcq.us-east-1.es.amazonaws.com
OPEN_SEARCH_SECRET_ID=a207891-ai-accel-qa-use1-masterUserPassphrase
DYNAMO_TABLE_NAME=a207891-ai-acceleration-conversations-ci
OPEN_AI_SECRET=a207891/ras-search/ai-acceleration/ci/open-ai-secret
# Currently, we are providing the base path for the Cobalt service to the Lab service. However, the Lab service currently lacks the necessary configuration for this integration.
COBALT_SEARCH_URL="https://ai-acceleration-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com"
LOGGER_FIX_LIST="gunicorn,uvicorn,langchain.chat_models.openai,ddtrace.profiling,celery,flower,kombu,redis,asyncio,boto,kms,amazon,azure,root"
S3_BUCKET=a207891-ai-acceleration-conversations-ci-use1
UDS_ENDPOINT=http://uds.int.next.qed.westlaw.com
EVENT_RECEIVER_ENDPOINT=https://event-receiver-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com/events/v2/event
DATADOG_API_SECRET_NAME=a207891-datadog-api-key-use1
DATADOG_METRIC_PREFIX=ras.search.conversations
OVERRIDE_HOST_NAME=local_developer
DATADOG_METRICS_ENABLED=True
EVENTS_ENABLED=True
RAG_SERVICE_TIMEOUT=540
TEMP_FOLDER=/temp
GCS_USER_SECRET=a207891/ras-search/ai-acceleration/ci/gcs-client-worker-secret
GCS_URL=https://entitlement-qa.gcs.int.thomsonreuters.com/v1/token
AWS_SES_SECRET=a207891/ras-search/ai-acceleration/qa/ses-credentials
DEFAULT_PROFILE_MAPPING_SECRET=a207891/ras-search/ai-acceleration/qa/profile-override-mappings
RAS_CONFIG_BASE_URL=https://ras-configuration-py-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
#LLM_PROXY_BASE_URL=http://localhost:8011/api/v1/proxy/azure
LLM_PROXY_BASE_URL=https://llm-proxy-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
LLM_PROXY_AZURE_URL_PATH=/api/v1/proxy/azure
LLM_PROXY_BEDROCK_URL_PATH=/api/v1/proxy/bedrock
LLM_PROXY_ENABLED=True
CLUSTER_SHORT=ras2
