import sys
from typing import Optional, List

from celery import shared_task, Task
from conversation_core.cobalt.utils import add_to_span
from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.tasks.task_contracts import ConversationTasks
from conversation_core.shared.tasks.conversation_tasks_base import ConversationTaskBaseV2, send_metrics
from ddtrace._trace.span import Span
from raslogger import LoggingFactory
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from config.settings import get_settings
from services.common_services import get_entitlement_client
from services.conversation_service import ConversationService

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
entitlement_client = get_entitlement_client(gcs_url=settings.GCS_URL)
conversation_service = ConversationService()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)


class ConversationTaskV2(ConversationTasks, Task):
    @shared_task(bind=True,
                 retry_backoff=True,
                 retry_kwargs={"max_retries": 1},
                 name='start_conversation_v2')
    def start_conversation_task(self,
                                is_new_conversation: bool,
                                user_id: str,
                                user_input: str,
                                answer_solution_profile: str,
                                jurisdictions_override: Optional[List[str]],
                                content_types_override: Optional[List[str]],
                                conversation_id: str,
                                conversation_entry_id: str,
                                conversation_action_type: ConversationActionType,
                                auth_token: str,
                                user_session: dict = None,
                                meta_data: Optional[dict] = None):
        meta_data["check_intent"] = False
        return ConversationTaskImpl().run_conversation(
            task=self,
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=user_input,
            answer_solution_profile=answer_solution_profile,
            jurisdictions_override=jurisdictions_override,
            content_types_override=content_types_override,
            conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id,
            conversation_action_type=conversation_action_type,
            auth_token=auth_token,
            user_session=user_session,
            meta_data=meta_data,
        )


def finish_task(span: Span,
                conversation_id: str,
                conversation_entry_id: str,
                answer_solution_profile: str,
                total_queued_time: float,
                total_execution_time: float,
                response_status_code: int,
                user_classification: str):
    send_metrics(conversation_id=conversation_id,
                 conversation_entry_id=conversation_entry_id,
                 answer_solution_profile=answer_solution_profile,
                 total_queued_time=total_queued_time,
                 total_execution_time=total_execution_time,
                 success=False,
                 user_classification=user_classification)

    add_to_span('http.status_code', f'{response_status_code}')
    span.set_exc_info(*sys.exc_info())


class ConversationTaskImpl(ConversationTaskBaseV2):
    def __init__(self):
        super().__init__(
            settings=settings,
            dynamo_db_v2=dynamo_db_v2,
            entitlement_client=entitlement_client,
            conversation_service=conversation_service,
            rag_service_wheel_name="labs-aalp-service",
        )
