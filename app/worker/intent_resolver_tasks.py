from typing import Optional, List

from celery import shared_task
from conversation_core.shared.tasks.task_contracts import IntentResolverTasks
from conversation_core.shared.tasks.intent_resolver_tasks_base import IntentResolverTaskBaseV3
from raslogger import LoggingFactory

from config.settings import get_settings
from services.common_services import get_entitlement_client
from services.intent_resolver_service import IntentResolverService

logger = LoggingFactory.get_logger(__name__)
settings = get_settings()
entitlement_client = get_entitlement_client(gcs_url=settings.GCS_URL)
intent_resolver_service = IntentResolverService()


class IntentResolverTask(IntentResolverTasks):
    @shared_task(
        bind=True,
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name="evaluate_intent"
    )
    def evaluate_intent_task(self,
                             is_new_conversation: bool,
                             user_id: str,
                             user_input: str,
                             answer_solution_profile: str,
                             jurisdictions_override: Optional[List[str]],
                             content_types_override: Optional[List[str]],
                             conversation_id: str,
                             conversation_entry_id: str,
                             conversation_action_type: str,
                             auth_token: str,
                             user_session: dict = None,
                             meta_data: Optional[dict] = None):
        return IntentResolverTaskImpl().evaluate_intent_task(
            task=self,
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=user_input,
            answer_solution_profile=answer_solution_profile,
            jurisdictions_override=jurisdictions_override,
            content_types_override=content_types_override,
            conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id,
            conversation_action_type=conversation_action_type,
            auth_token=auth_token,
            user_session=user_session,
            meta_data=meta_data,
        )


class IntentResolverTaskImpl(IntentResolverTaskBaseV3):
    def __init__(self):
        super().__init__(
            settings=settings,
            intent_resolver_service=intent_resolver_service,
            rag_service_wheel_name="labs-aalp-service",
            entitlement_client=entitlement_client
        )
