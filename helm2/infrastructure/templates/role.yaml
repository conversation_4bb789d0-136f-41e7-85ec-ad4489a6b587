apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ .Values.application.name }}-secret-reader-role-{{ .Values.environment }}
  labels:
{{ include "mychart.common_labels" .  | indent 4 }}
  annotations:
    app.tr.com/deployment-specification: "https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/main/helm2/infrastructure/templates/role.yaml"
{{ include "mychart.resource_annotations" . | indent 4 }}
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list"]  # Specify the required verbs (permissions)
