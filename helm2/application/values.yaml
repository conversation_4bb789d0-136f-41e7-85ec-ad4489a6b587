deployment :
  replicaCount:
    dev: 2
    ci: 2
    int: 15
    qa: 15
    prod: 15
  terminationDrainDuration: 600s
  terminationGracePeriodSeconds: 600
  image:
    containerPort : 8000
    pullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 1
        memory: 2Gi
      requests:
        cpu: 1
        memory: 2Gi
    livenessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      timeoutSeconds: 10
      periodSeconds: 30
      failureThreshold: 5
    startupProbe:
      periodSeconds: 10
      failureThreshold: 10
    readinessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      periodSeconds: 20
hpa:
  max:
    dev: 2
    ci: 2
    int: 15
    qa: 15
    prod: 15
  cpu:
    targetAverageUtilization: 50
  memory:
    targetAverageUtilization: 65
poddb:
  maxUnavailable: "50%"

virtualservice:
  port:
    number: 80
  weight: 100

destination:
  subset: app-deployment
