apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{ .Chart.Name }}-destination-rule-{{ .Values.environment }}-{{ .Chart.AppVersion }}
  annotations:
    app.tr.com/deployment-specification: "https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/main/helm2/application/templates/destination-rule.yaml"
{{ include "mychart.resource_annotations" . | indent 4 }}
spec:
  host: {{ .Chart.Name }}-infra-service-{{ .Values.environment }}  # The k8 service
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
  subsets:  # Break down the service into subsets based on the labels below.
    - name: {{ .Values.destination.subset }}
      labels:
        version: {{ .Chart.AppVersion }}
{{ include "mychart.common_labels" .  | indent 8 }}
      trafficPolicy:
        loadBalancer:
          simple: ROUND_ROBIN