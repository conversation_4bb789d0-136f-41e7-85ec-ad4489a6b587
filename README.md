<!-- START badge-template.html --><svg fill="none" viewBox="0 0 120 120" width="120" height="120" xmlns="http://www.w3.org/2000/svg">
  <foreignObject width="100%" height="100%">
    <div xmlns="http://www.w3.org/1999/xhtml">
      <a href="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/tr/ras-search_ai-rag-westlaw-claims-explorer/badges/intent-resolver-settings/last-badge-update.svg" target="_blank">
        <img alt="Last Updated" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/badges/intent-resolver-settings/last-badge-update.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/actions/runs/17133348966" target="_blank">
        <img alt="CI Build" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/actions/workflows/python-build.yml/badge.svg?branch=intent-resolver-settings">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/pulls?q=is:pr+created:%3C%3D2025-08-14+is%3Aopen" target="_blank">
      <img alt="Stale Pull Requests" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/badges/intent-resolver-settings/stale-pr-count.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/labels/dependencies?q=+is%3Aopen" target="_blank">
      <img alt="Dated Dependencies" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/badges/intent-resolver-settings/dated-dependency-count.svg">
      </a>
      <br />
      <a href="" target="_blank">
        <img alt="Code Coverage" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/badges/intent-resolver-settings/code-coverage.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/runs/48603277952" target="_blank">
        <img alt="Lines of Code" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/badges/intent-resolver-settings/lines-of-code.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/releases/latest" target="_blank">
      <img alt="Latest Release" src="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/tr-cicd-resources/badges/main/latest-release.svg">
      </a>
    </div>
  </foreignObject>
</svg>

<!-- END badge-template.html -->
# ras-search_ai-rag-westlaw-claims-explorer
Claims Explorer product for Westlaw back-end codebase

To run locally

Set the following env vars in the run config.

    PYTHONUNBUFFERED=1;
    DD_ENV=local;
    DD_PROFILING_ENABLED=false;
    DD_SERVICE=ai-conversations;
    DD_TRACE_ENABLED=True;
    DD_VERSION=0.0.1;
    RESOURCES_DIR=./app/config/resources
    HOSTNAME=[Your workstation name]

The working directory needs to be set to the root not the /app folder.


## To run locally with docker
AI conversations now relies on Redis, DynamoDb, and DD agent to be run locally via docker desktop.

1. Install docker desktop.
2. After loading docker desktop run the following to pull the right images.
    ```
    docker pull redis:latest
    docker pull amazon/dynamodb-local
    docker pull datadog/agent:latest
    ```
   
3. Run the following to start the containers.
    ```
   docker run -d -p 6379:6379 --name redis redis
   docker run -d -p 8000:8000 --name dynamodb amazon/dynamodb-local
   docker run -d -p 8125:8125 -p 8126:8126  -e DD_HOSTNAME=local.developer -e DD_ENV=local -e DD_API_KEY=[get this from secret manager] --name datadog_agent datadog/agent:latest

4. Cloud-tool into the pre-prod ras search account.
   ```
   cloud-tool login -u 'mgmt\your_user_id' -p your_password
   tr-ras-search-preprod (653661534649): human-role/a207891-PowerUser2 (RAS Search)

5. You are now able to launch the ai-conversations, workers, and monitor locally.

**To have a better experience, you will need to install postman.**
**You will also need a pre-prod gcs token**

