# Build stage
ARG REGION_LONG
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11-dev AS build

USER root

RUN apk add openssl && apk add gzip

RUN apk update

# Disable checksum as it's failing
ENV VERIFY_CHECKSUM=false

COPY /scripts /scripts

RUN chmod 700 /scripts/get_helm.sh
RUN /scripts/get_helm.sh
RUN rm -rf /scripts

# Create home directory with proper permissions before switching user
RUN mkdir -p /home/<USER>/home/<USER>

# Create a custom temp directory inside home
RUN mkdir -p /home/<USER>/tmp && chmod 750 /home/<USER>/tmp

# Switch to non-root user and set working directory
USER 65532:65532
ENV HOME=/home/<USER>
WORKDIR /home/<USER>

# Copy only requirements first to leverage Docker cache
COPY --chown=65532:65532 requirements.txt .

ARG PIP_EXTRA_INDEX_URL
ARG ARTIFACTORY_USER
ARG ARTIFACTORY_TOKEN

ENV PIP_EXTRA_INDEX_URL=https://${ARTIFACTORY_USER}:${ARTIFACTORY_TOKEN}@tr1.jfrog.io/tr1/api/pypi/pypi/simple

# Create venv and install dependencies
RUN python -m venv /home/<USER>/venv
ENV PATH="/home/<USER>/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt && pip cache purge

# Copy application code
COPY --chown=65532:65532 /app /home/<USER>/app

# Final stage
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11
# Add Maintainer Info
LABEL maintainer="<EMAIL>"
# https://techtoc.thomsonreuters.com/non-functional/security/container-security/container-k8s-labels/#container-labels
LABEL com.tr.application-asset-insight-id="207891"
LABEL org.opencontainers.image.authors="<EMAIL>"
LABEL com.tr.service-contact="<EMAIL>"
LABEL org.opencontainers.image.source="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer/blob/main/Dockerfile"
# app version will be filled in by bakespec during the bake stage
LABEL org.opencontainers.image.version="APPVERSION"
LABEL org.opencontainers.image.vendor="Thomson Reuters"
LABEL org.opencontainers.image.url="https://github.com/tr/ras-search_ai-rag-westlaw-claims-explorer"

USER 65532:65532
ENV HOME=/home/<USER>
WORKDIR /home/<USER>

# Copy everything from build stage, including the tmp directory
COPY --from=build --chown=65532:65532 /home/<USER>/home/<USER>

# Copy helm separately
COPY --from=build --chown=65532:65532 /usr/local/bin/helm /usr/local/bin/helm

ENV PATH="/home/<USER>/venv/bin:/usr/local/bin:$PATH"
ENV PYTHONPATH="/home/<USER>/app:$PYTHONPATH"

# Set TMPDIR to the custom temp directory we created
ENV TMPDIR="/home/<USER>/tmp"

# Enable FIPS endpoints for all AWS services
ENV AWS_USE_FIPS_ENDPOINT=true

ENTRYPOINT ["celery","-A","main.celery_app","worker","--loglevel=info","--concurrency=1","--without-mingle","--without-gossip","--without-heartbeat"]