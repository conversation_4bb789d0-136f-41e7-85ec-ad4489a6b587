from unittest.mock import patch, MagicMock, ANY

import pytest
from celery.app.task import Context
from celery.contrib.abortable import AbortableTask
from celery.utils.threads import LocalStack
from conversation_core.shared.enums import (ConversationActionType, IntentClassification, )
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.models.intent_resolver import IntentResolverOutput

test_user_id = "user_id"
test_user_input = "Will this test pass?"
test_conversation_id = "conversation_id"
test_conversation_entry_id = "conversation_entry_id"
test_auth_token = "auth_token"


class TestIntentResolverService:
    @pytest.fixture
    def intent_resolver_service(self, mock_conversation_db_instance, mock_conversation_db_v2_instance):
        from services.intent_resolver_service import IntentResolverService

        return IntentResolverService()

    @pytest.fixture
    def mock_worker_task(self):
        mock_worker_task = MagicMock()
        mock_worker_task.get_header_value.return_value = "user_classification"
        mock_worker_task.task = AbortableTask()
        mock_worker_task.task.request_stack = LocalStack()
        mock_worker_task.task.request_stack.push(Context(headers={}))
        return mock_worker_task

    @pytest.fixture
    def profile(self):
        answer_profile = AnswerProfile(name="westlaw_claims_explorer_profile2", default_fermi_jurisdiction=["ALLFEDS"],
                                       rag_solution="wl-claims-explorer-rag-v01", default_result_size=1000,
                                       sender_addr="<EMAIL>", intent_profile="wl-intent-resolver-v004",
                                       auto_submit_intent_classifications=[IntentClassification.LEGAL,
                                                                           IntentClassification.ILLEGAL_INFORMATION],
                                       additionalProfileMeta={"max_entries_per_conversation": 2,
                                                              "max_conversation_time_hours": 24,
                                                              "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                                              "supported_answer_content_types": ['CASE', 'STATUTE',
                                                                                                 'REGULATION',
                                                                                                 'KNOWHOW',
                                                                                                 'ANALYTICAL']}

                                       )
        answer_profile.include_additional_content_snippets = False
        return answer_profile

    class TestCheckIntent:
        @pytest.mark.asyncio
        @patch("services.intent_resolver_service.intent_resolver_check_intent")
        @patch("services.intent_resolver_service.dynamo_db_v2")
        async def test_check_intent(self, mock_dynamo_db_v2, mock_check_intent, intent_resolver_service, profile,
                                    mock_worker_task):
            mock_dynamo_db_v2.get_conversation.return_value = MagicMock()
            mock_dynamo_db_v2.update_conversation_entry.return_value = MagicMock()
            expected_intent_resolver_output = IntentResolverOutput(intent_category=IntentClassification.NON_LEGAL,
                                                                   is_sufficient=True)
            mock_check_intent.return_value = expected_intent_resolver_output

            actual_intent_evaluation_result = await intent_resolver_service.check_intent(is_new_conversation=True,
                                                                                         user_id=test_user_id,
                                                                                         user_input=test_user_input,
                                                                                         answer_solution_profile=profile.rag_solution,
                                                                                         conversation_id=test_conversation_id,
                                                                                         conversation_entry_id=test_conversation_entry_id,
                                                                                         conversation_action_type=ConversationActionType.RAG,
                                                                                         auth_token=test_auth_token,
                                                                                         worker_task=mock_worker_task,
                                                                                         answer_profile=profile,
                                                                                         jurisdictions_override=[],
                                                                                         content_types_override=[],
                                                                                         meta_data={}, )

            assert expected_intent_resolver_output == actual_intent_evaluation_result
            mock_check_intent.assert_called_once_with(message=test_user_input, intent_settings=ANY,
                                                      fermi_jurisdictions=['ALLFEDS'], conversation_history=None,
                                                      profile="wl-intent-resolver-v004", content_types=ANY,
                                                      raise_exceptions=False)

    class TestAutoSubmitIntent:
        @patch("services.intent_resolver_service.conversation_v2")
        @patch("time.time", lambda: 1.0)
        def test_auto_submit_intent(self, mock_conversation_task, intent_resolver_service, profile, mock_worker_task):
            intent_resolver_service.auto_submit_intent(is_new_conversation=True, user_id=test_user_id,
                                                       user_input=test_user_input,
                                                       answer_solution_profile=profile.rag_solution,
                                                       jurisdictions_override=[], content_types_override=[],
                                                       conversation_id=test_conversation_id,
                                                       conversation_entry_id=test_conversation_entry_id,
                                                       conversation_action_type=ConversationActionType.RAG,
                                                       auth_token=test_auth_token, worker_task=mock_worker_task,
                                                       answer_profile=profile, meta_data={}, )

            mock_conversation_task.start_conversation_task.apply_async.assert_called_once_with(
                args=[True, test_user_id, test_user_input, profile.rag_solution, [], [], test_conversation_id,
                      test_conversation_entry_id, ConversationActionType.RAG, test_auth_token, None, {}, ], kwargs=None,
                task_id=test_conversation_entry_id, headers={"time_sent": 1.0, "conversation_id": test_conversation_id,
                                                             "conversation_entry_id": test_conversation_entry_id,
                                                             "user_classification": "unknown", "green": False, })
