import asyncio
from unittest.mock import MagicMock, patch, AsyncMock

import pytest
from celery.app.task import Context
from celery.contrib.abortable import AbortableTask
from celery.utils.threads import LocalStack

from app.config.settings import ClaimExRAGSettings
from app.model import constants as const
from app.model.claims_explorer_result import ClaimsExplorerResult
from services.openai_service import OpenAIService, ClaimsExplorationTimeoutException


class TestOpenAIService:
    service = OpenAIService()

    @pytest.fixture
    def mock_worker_task(self):
        mock_worker_task = MagicMock()
        mock_worker_task.get_header_value.return_value = "user_classification"
        mock_worker_task.task = AbortableTask()
        mock_worker_task.task.request_stack = LocalStack()
        mock_worker_task.task.request_stack.push(Context(headers={}))
        return mock_worker_task

    @pytest.mark.asyncio
    async def test_claims_explorer_timeout(self, mock_worker_task):
        rag_settings = ClaimExRAGSettings()
        rag_settings.tags = ["test_tag"]
        conversation_id = "test_conversation_id"
        conversation_entry_id = "test_conversation_entry_id"
        question = "test_question"
        worker_task = mock_worker_task
        jurisdictions = ["test_jurisdiction"]
        is_mock_request = False

        with patch.object(self.service, 'do_ai_claims_exploration', side_effect=asyncio.TimeoutError()):
            with pytest.raises(ClaimsExplorationTimeoutException):
                await self.service.claims_explorer(rag_settings, conversation_id, conversation_entry_id, question,
                                                   worker_task, jurisdictions, is_mock_request)

    @pytest.mark.asyncio
    async def test_claims_explorer_success(self, mock_worker_task):
        rag_settings = ClaimExRAGSettings()
        rag_settings.tags = ["test_tag"]
        conversation_id = "test_conversation_id"
        conversation_entry_id = "test_conversation_entry_id"
        question = "test_question"
        jurisdictions = ["test_jurisdiction"]
        is_mock_request = False

        expected_result = ClaimsExplorerResult()
        with patch.object(self.service, 'do_ai_claims_exploration', return_value=expected_result):
            result = await self.service.claims_explorer(rag_settings, conversation_id, conversation_entry_id, question,
                                                        mock_worker_task, jurisdictions, is_mock_request)
            assert result == expected_result

    @pytest.mark.asyncio
    async def test_do_ai_claims_exploration(self):
        rag_settings = ClaimExRAGSettings()
        worker_task = MagicMock()
        jurisdictions = ["jurisdiction1", "jurisdiction2"]
        is_mock_request = False

        with patch.object(self.service, 'do_ai_claims_exploration', new_callable=AsyncMock) as mock_method:
            await self.service.do_ai_claims_exploration(rag_settings, "conversation_id", "conversation_entry_id",
                                                        "question", worker_task, jurisdictions, is_mock_request)
            mock_method.assert_called_once_with(rag_settings, "conversation_id", "conversation_entry_id", "question",
                                                worker_task, jurisdictions, is_mock_request)

    @pytest.mark.asyncio
    async def test_do_ai_claims_exploration_exception(self):
        rag_settings = ClaimExRAGSettings()
        worker_task = MagicMock()
        jurisdictions = ["jurisdiction1", "jurisdiction2"]
        is_mock_request = False

        with patch.object(self.service, 'filter_relevant_claims', side_effect=Exception("Test exception")):
            with pytest.raises(Exception):
                await self.service.do_ai_claims_exploration(rag_settings, "conversation_id", "conversation_entry_id",
                                                            "question", worker_task, jurisdictions, is_mock_request)

    @pytest.mark.asyncio
    async def test_filter_relevant_claims(self):
        rag_settings = ClaimExRAGSettings()
        jurisdictions = ["jurisdiction1", "jurisdiction2"]
        is_mock_request = False
        job_metrics = MagicMock()

        with patch.object(self.service, 'filter_relevant_claims', new_callable=AsyncMock) as mock_method:
            await self.service.filter_relevant_claims(rag_settings, "question", jurisdictions, is_mock_request,
                                                      job_metrics)
            mock_method.assert_called_once_with(rag_settings, "question", jurisdictions, is_mock_request, job_metrics)

    @pytest.mark.asyncio
    async def test_get_relevant_claims(self):
        rag_settings = ClaimExRAGSettings()
        jurisdictions = ["jurisdiction1", "jurisdiction2"]
        is_mock_request = False
        job_metrics = MagicMock()

        with patch.object(self.service, 'get_relevant_claims', new_callable=AsyncMock) as mock_method:
            await self.service.get_relevant_claims(rag_settings, "question", ["filtered_claim1", "filtered_claim2"],
                                                   jurisdictions, is_mock_request, job_metrics)
            mock_method.assert_called_once_with(rag_settings, "question", ["filtered_claim1", "filtered_claim2"],
                                                jurisdictions, is_mock_request, job_metrics)

    def test_format_gpt35_responses(self):
        ai_response = "1|Yes\n2|No"
        with patch('services.openai_service.claims_data_service.get_records_by_claims_id',
                   return_value=[{"ClaimsID": "1"}]):
            result = self.service.format_gpt35_responses(ai_response)
            assert result == [{'Answer': 'Yes', 'ClaimsID': '1'}, {'Answer': 'No', 'ClaimsID': '2'}]

    @pytest.mark.asyncio
    async def test_restructure_positive_claims_matches(self):
        with patch.object(self.service, 'restructure_positive_claims_matches', new_callable=AsyncMock) as mock_method:
            await self.service.restructure_positive_claims_matches(["relevant_claim1", "relevant_claim2"])
            mock_method.assert_called_once_with(["relevant_claim1", "relevant_claim2"])

    @pytest.mark.asyncio
    async def test_restructure_positive_claims_matches_empty(self):
        result = await self.service.restructure_positive_claims_matches([])
        assert result == {}

    @pytest.mark.asyncio
    async def test_generate_claims_explanation(self):
        rag_settings = ClaimExRAGSettings()
        is_mock_request = False
        job_metrics = MagicMock()

        with patch.object(self.service, 'generate_claims_explanation', new_callable=AsyncMock) as mock_method:
            await self.service.generate_claims_explanation(rag_settings, "question", {"positive_claim1": "explanation1",
                                                                                      "positive_claim2": "explanation2"},
                                                           is_mock_request, job_metrics)
            mock_method.assert_called_once_with(rag_settings, "question",
                                                {"positive_claim1": "explanation1", "positive_claim2": "explanation2"},
                                                is_mock_request, job_metrics)

    @pytest.mark.asyncio
    async def test_run_ai_claims_explanation(self):
        with patch.object(self.service, 'run_ai_claims_explanation', new_callable=AsyncMock) as mock_method:
            await self.service.run_ai_claims_explanation()
            mock_method.assert_called_once()

    @pytest.mark.asyncio
    async def test_format_validate_explanation(self):
        with patch.object(self.service, 'format_validate_explanation', new_callable=AsyncMock) as mock_method:
            await self.service.format_validate_explanation()
            mock_method.assert_called_once()

    @pytest.mark.asyncio
    async def test_populate_royalty_ids(self):
        with patch.object(self.service, 'populate_royalty_ids', new_callable=AsyncMock) as mock_method:
            await self.service.populate_royalty_ids()
            mock_method.assert_called_once()

    def test_populate_royalty_ids(self):
        rag_settings = ClaimExRAGSettings()
        claim_results = [{"source_guid": "guid1"}, {"source_guid": "guid2"}]

        with patch('services.openai_service.OpenSearch') as mock_open_search:
            mock_open_search.return_value.execute_doc_guid_open_search.return_value = {
                "docs": [{"_id": "guid1", "found": True, "_source": {"doc_status": "Live", "royalty_id": "R001"}},
                         {"_id": "guid2", "found": True, "_source": {"doc_status": "Live", "royalty_id": "R002"}}]}
            result = self.service.populate_royalty_ids(rag_settings, claim_results)
            assert result[0]["royalty_id"] == "R001"
            assert result[1]["royalty_id"] == "R002"

    def test_sort_for_output(self):
        docs = [{"royalty_id": "R002"}, {"royalty_id": "R001"}]
        with patch('services.openai_service.do_sort', return_value=[{"royalty_id": "R001"}, {"royalty_id": "R002"}]):
            result = self.service.sort_for_output("statutory", docs, False)
            assert result[0]["royalty_id"] == "R001"
            assert result[1]["royalty_id"] == "R002"

    def test_split_list_to_sublists_with_max_size(self):
        input_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        result = self.service.split_list_to_sublists_with_max_size(input_list, 3)
        assert result == [[1, 2, 3], [4, 5, 6], [7, 8, 9], [10]]

    def test_sanitize_none(self):
        assert self.service.sanitize_none("None") is None
        assert self.service.sanitize_none("NA") is None
        assert self.service.sanitize_none("Test") == "Test"
        assert self.service.sanitize_none(None) is None

    def test_cause_grouper(self):
        cause_list = [{"ClaimsID": "1", "Title": "T1", "Citation": "C1", "Long Desc": "LD1", "Short Desc": "SD1"},
                      {"ClaimsID": "2", "Title": "T2", "Citation": "C2", "Long Desc": "LD2", "Short Desc": "SD2"}]
        result = self.service.cause_grouper(cause_list)
        assert len(result) == 1
        assert len(result[0]) == 2

    def test_format_chunk(self):
        chunk = {"ClaimsID": "1", "Title": "T1", "Citation": "C1", "Long Desc": "LD1", "Short Desc": "SD1"}
        result = self.service.format_chunk(chunk)
        assert result == "ClaimsID=1 | Title=T1 | Citation=C1 | Long Desc=LD1 | Short Desc=SD1"

    def test_format_grouped_chunks(self):
        chunks = [{"ClaimsID": "1", "Title": "T1", "Citation": "C1", "Long Desc": "LD1", "Short Desc": "SD1"},
                  {"ClaimsID": "2", "Title": "T2", "Citation": "C2", "Long Desc": "LD2", "Short Desc": "SD2"}]
        result = self.service.format_grouped_chunks(chunks)
        expected = "ClaimsID=1 | Title=T1 | Citation=C1 | Long Desc=LD1 | Short Desc=SD1||\\nClaimsID=2 | Title=T2 | Citation=C2 | Long Desc=LD2 | Short Desc=SD2###\\n"
        assert result == expected

    def test_format_validate_explanation_exception(self):
        raw_explanations = {"ClaimsID": "1", "Answer": "Yes", "Discussion": "Test"}

        with patch('services.openai_service.claims_data_service.get_records_by_claims_id', return_value=[]):
            result = self.service.format_validate_explanation(raw_explanations)
            assert result == {}

    def test_cause_grouper_large_input(self):
        cause_list = [
            {"ClaimsID": str(i), "Title": f"T{i}", "Citation": f"C{i}", "Long Desc": f"LD{i}", "Short Desc": f"SD{i}"}
            for i in range(1000)]
        result = self.service.cause_grouper(cause_list)
        assert len(result) > 1

    @pytest.mark.asyncio
    async def test_get_relevant_claims_empty_result(self):
        rag_settings = ClaimExRAGSettings()
        jurisdictions = ["jurisdiction1"]
        is_mock_request = False
        job_metrics = MagicMock()

        with patch.object(self.service, 'run_ai_claims_answer', return_value=([], [], [])):
            result = await self.service.get_relevant_claims(rag_settings, "question", ["filtered_claim1"],
                                                            jurisdictions, is_mock_request, job_metrics)
            assert result == []

    def test_format_gpt35_responses_invalid_input(self):
        ai_response = "invalid|response"
        result = self.service.format_gpt35_responses(ai_response)
        assert result == []

    @pytest.mark.asyncio
    async def test_restructure_positive_claims_matches_multiple_matches(self):
        positive_claim_matches = [{"ClaimsID": "1", "Answer": "Yes"}, {"ClaimsID": "1", "Answer": "No"}]
        with patch('services.openai_service.claims_data_service.get_records_by_claims_id', return_value=[
            {"ClaimsID": "1", "StatuteTitle": "Title1", "subsection_cite": "Cite1", "long_description": "Long1",
             "short_description": "Short1"},
            {"ClaimsID": "1", "StatuteTitle": "Title2", "subsection_cite": "Cite2", "long_description": "Long2",
             "short_description": "Short2"}]):
            result = await self.service.restructure_positive_claims_matches(positive_claim_matches)
            assert "1" not in result

    def test_cause_grouper_empty_input(self):
        result = self.service.cause_grouper([])
        assert result == []

    def test_cause_grouper_large_chunks(self):
        cause_list = [
            {"ClaimsID": "1", "Title": "T" * 1000, "Citation": "C", "Long Desc": "LD" * 1000, "Short Desc": "SD"}]
        result = self.service.cause_grouper(cause_list)
        assert len(result) >= 1

    @pytest.mark.asyncio
    async def test_do_ai_claims_exploration_all_steps(self):
        rag_settings = ClaimExRAGSettings()
        worker_task = MagicMock()
        jurisdictions = ["jurisdiction1"]
        is_mock_request = False

        with patch.object(self.service, 'filter_relevant_claims', return_value=["claim1"]), patch.object(self.service,
                                                                                                         'get_relevant_claims',
                                                                                                         return_value=[{
                                                                                                             "ClaimsID": "1",
                                                                                                             "Answer": "Yes"}]), patch.object(
            self.service, 'restructure_positive_claims_matches', return_value={"1": {"ClaimsID": "1"}}), patch.object(
            self.service, 'generate_claims_explanation', return_value=ClaimsExplorerResult()):
            result = await self.service.do_ai_claims_exploration(rag_settings, "conversation_id",
                                                                 "conversation_entry_id", "question", worker_task,
                                                                 jurisdictions, is_mock_request)
            assert isinstance(result, ClaimsExplorerResult)

    def test_remap_jurisdictions(self):
        const.JURISDICTION_MAPPINGS = {"California": "CA"}
        assert self.service.remap_jurisdictions(["California"]) == ["CA"]
        assert self.service.remap_jurisdictions(["Unknown"]) == []
        assert self.service.remap_jurisdictions(None) == ["Federal"]
        assert self.service.remap_jurisdictions([]) == ["Federal"]

    def test_cause_grouper_mixed_lengths(self):
        cause_list = [{"ClaimsID": "1", "Title": "T1", "Citation": "C1", "Long Desc": "LD1", "Short Desc": "SD1"},
                      {"ClaimsID": "2", "Title": "T2" * 500, "Citation": "C2", "Long Desc": "LD2" * 500,
                       "Short Desc": "SD2"},
                      {"ClaimsID": "3", "Title": "T3", "Citation": "C3", "Long Desc": "LD3", "Short Desc": "SD3"}]
        result = self.service.cause_grouper(cause_list)
        assert len(result) == 1
        assert len(result[0]) == 3

