from datetime import datetime
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from aalp_service.v2.rag.dataclasses import RagServiceOutput
from celery.app.task import Context
from celery.contrib.abortable import AbortableTask
from celery.utils.threads import LocalStack
from conversation_core.cobalt.profiles import SupportedAnswerContentTypes
from conversation_core.shared.enums import ConversationActionType, IntentClassification
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.models.errors import ErrorModel
from conversation_core.shared.models.v2.conversation_entry import ConversationEntry

from app.services.conversation_service import WestlawClaimsExplorerAnswer, ConversationService
from model.claims_explorer_result import ClaimsExplorerResult
from utils.custom_exceptions import MaliciousContentException

test_user_id = "user_id"
test_user_input = "Will this test pass?"
test_answer = "42"
test_conversation_id = "conversation_id"
test_conversation_entry_id = "conversation_entry_id"
test_auth_token = "auth_token"

test_conversation_history = [
    ConversationEntry(conversation_entry_id='1', status='Complete', user_input='What is the meaning of life?',
                      timestamp=int(datetime.strptime('2021-09-01T00:00:00Z', '%Y-%m-%dT%H:%M:%SZ').timestamp())),
    ConversationEntry(conversation_entry_id='2', status='Complete',
                      user_input='What is your favorite programming language?',
                      timestamp=int(datetime.strptime('2021-09-02T00:00:00Z', '%Y-%m-%dT%H:%M:%SZ').timestamp())),
    ConversationEntry(conversation_entry_id='3', status='Complete', user_input='What is your name?',
                      timestamp=int(datetime.strptime('2021-09-03T00:00:00Z', '%Y-%m-%dT%H:%M:%SZ').timestamp()))]


@pytest.fixture
def conversation_service():
    return ConversationService()


class TestWestlawClaimsExplorerAnswer:
    def test_get_results(self):
        claims_explorer_result = ClaimsExplorerResult()
        westlaw_claims_explorer_answer = WestlawClaimsExplorerAnswer(system_output=claims_explorer_result)
        results = westlaw_claims_explorer_answer.get_results()
        assert isinstance(results, dict)


class TestGetErrorDetails:

    @patch("services.conversation_service.get_error_retryable")
    @patch("services.conversation_service.get_error_code")
    @patch("services.conversation_service.get_error_message")
    def test_get_error_details(self, mock_get_error_message, mock_get_error_code, mock_get_error_retryable,
                               conversation_service):
        retryable = True
        error_code = 500
        error_message = "There was an error processing your request. Please try again later."

        mock_get_error_retryable.return_value = retryable
        mock_get_error_code.return_value = error_code
        mock_get_error_message.return_value = error_message

        expected_error_details = ErrorModel()
        expected_error_details.is_retryable = retryable
        expected_error_details.code = error_code
        expected_error_details.message = error_message

        actual_error_details = conversation_service.get_error_details(ex=Exception())

        assert retryable == actual_error_details.is_retryable
        assert error_code == actual_error_details.code
        assert error_message == actual_error_details.message


class TestGenerateAnswer:

    @pytest.fixture
    def rag_service_output(self):
        mock_user_input = {"message": test_user_input, "content_types": [], "fermi_jurisdictions": []}
        return RagServiceOutput(user_input=mock_user_input,
                                generated_answer={"answer_text": test_answer, "search_results": []},
                                intent={"user_input": mock_user_input, "annotations": []}, )

    @pytest.fixture
    def mock_worker_task(self):
        mock_worker_task = MagicMock()
        mock_worker_task.get_header_value.return_value = "user_classification"
        mock_worker_task.task = AbortableTask()
        mock_worker_task.task.request_stack = LocalStack()
        mock_worker_task.task.request_stack.push(Context(headers={}))
        return mock_worker_task

    @pytest.fixture
    def profile(self):
        answer_profile = AnswerProfile(name="westlaw_claims_explorer_profile2", default_fermi_jurisdiction=["ALLCASES"],
                                       rag_solution="wl-claims-explorer-rag-v01", default_result_size=1000,
                                       sender_addr="<EMAIL>", intent_profile="wl-intent-resolver-v004",
                                       auto_submit_intent_classifications=[IntentClassification.LEGAL,
                                                                           IntentClassification.ILLEGAL_INFORMATION],
                                       additionalProfileMeta={"max_entries_per_conversation": 2,
                                                              "max_conversation_time_hours": 24,
                                                              "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                                              "supported_answer_content_types": ['CASE', 'STATUTE',
                                                                                                 'REGULATION',
                                                                                                 'KNOWHOW',
                                                                                                 'ANALYTICAL']}

                                       )
        answer_profile.include_additional_content_snippets = False
        answer_profile.additional_content_types = []
        return answer_profile

    @pytest.mark.asyncio
    @patch('app.services.conversation_service.openai_service.claims_explorer', new_callable=AsyncMock)
    @patch('app.services.conversation_service.dynamo_db_v2')
    async def test_generate_answer(self, mock_dynamo_db_v2, mock_claims_explorer, mock_worker_task,
                                   conversation_service, profile):
        conversation_action_type = ConversationActionType.RAG

        mock_dynamo_db_v2.get_conversation.return_value = MagicMock()
        mock_dynamo_db_v2.update_conversation_entry.return_value = MagicMock()
        mock_claims_explorer.return_value = ClaimsExplorerResult()
        result = await conversation_service.generate_answer(is_new_conversation=True, user_id=test_user_id,
                                                            user_input=test_user_input,
                                                            answer_solution_profile=profile.rag_solution,
                                                            jurisdictions_override=[], content_types_override=[],
                                                            conversation_id=test_conversation_id,
                                                            conversation_entry_id=test_conversation_entry_id,
                                                            conversation_action_type=conversation_action_type,
                                                            auth_token=test_auth_token, answer_profile=profile, tags=[],
                                                            worker_task=mock_worker_task,
                                                            user_session={"sessionId": "test_session_id"},
                                                            meta_data={"rag_version": "0.0.1", "app_version": "0.0.2",
                                                                       "python_version": "3.0.0",
                                                                       "conversation_type": "Initial",
                                                                       "check_intent": False, },
                                                            conversation_history=test_conversation_history, )

        assert result is not None
        mock_claims_explorer.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_answer_should_raise_malicious_content_exception_when_prompt_injection_test_case(self,
                                                                                                            conversation_service,
                                                                                                            profile,
                                                                                                            mock_worker_task):
        with pytest.raises(MaliciousContentException) as exception:
            await conversation_service.generate_answer(is_new_conversation=True, user_id=test_user_id,
                                                       user_input="prompt injection test case",
                                                       answer_solution_profile=profile.name, jurisdictions_override=[],
                                                       content_types_override=[], conversation_id=test_conversation_id,
                                                       conversation_entry_id=test_conversation_entry_id,
                                                       conversation_action_type=ConversationActionType.RAG,
                                                       auth_token=test_auth_token, answer_profile=profile, tags=[],
                                                       worker_task=mock_worker_task,
                                                       user_session={"sessionId": "test_session_id"},
                                                       meta_data={"rag_version": "0.0.1", "app_version": "0.0.2",
                                                                  "python_version": "3.0.0",
                                                                  "conversation_type": "Initial",
                                                                  "check_intent": False, }, )
        assert "Prompt injection test case" == str(exception.value)

    @pytest.mark.asyncio
    @patch('app.services.conversation_service.openai_service.claims_explorer', new_callable=AsyncMock)
    @patch('app.services.conversation_service.dynamo_db_v2')
    async def test_generate_answer_should_apply_overrides_when_overrides_specified(self, mock_dynamo_db_v2,
                                                                                   mock_claims_explorer,
                                                                                   mock_worker_task,
                                                                                   conversation_service, profile,
                                                                                   rag_service_output):
        mock_dynamo_db_v2.get_conversation.return_value = MagicMock()
        mock_dynamo_db_v2.update_conversation_entry.return_value = MagicMock()
        mock_claims_explorer.return_value = ClaimsExplorerResult()
        jurisdictions_override = ["WA-CS"]
        content_types_override = [SupportedAnswerContentTypes.CASE, SupportedAnswerContentTypes.REGULATION]
        expected_system_output = ClaimsExplorerResult(response="system response")
        expected_generate_answer = WestlawClaimsExplorerAnswer(rag_service_output=rag_service_output,
                                                               system_output=expected_system_output)

        actual_answer = await conversation_service.generate_answer(is_new_conversation=True, user_id=test_user_id,
                                                                   user_input=test_user_input,
                                                                   answer_solution_profile=profile.name,
                                                                   jurisdictions_override=jurisdictions_override,
                                                                   content_types_override=content_types_override,
                                                                   conversation_id=test_conversation_id,
                                                                   conversation_entry_id=test_conversation_entry_id,
                                                                   conversation_action_type=ConversationActionType.RAG,
                                                                   auth_token=test_auth_token, answer_profile=profile,
                                                                   tags=[], worker_task=mock_worker_task,
                                                                   conversation_history=test_conversation_history,
                                                                   user_session={"sessionId": "test_session_id"},
                                                                   meta_data={"rag_version": "0.0.1",
                                                                              "app_version": "0.0.2",
                                                                              "python_version": "3.0.0",
                                                                              "conversation_type": "Initial",
                                                                              "check_intent": False}, )

        assert expected_generate_answer == actual_answer

    @pytest.mark.asyncio
    @patch("services.conversation_service.check_intent")
    @patch('app.services.conversation_service.openai_service.claims_explorer', new_callable=AsyncMock)
    @patch('app.services.conversation_service.dynamo_db_v2')
    async def test_generate_answer_should_not_run_intent_when_intent_flag_is_false(self, mock_dynamo_db_v2,
                                                                                   mock_claims_explorer,
                                                                                   mock_check_intent, mock_worker_task,
                                                                                   conversation_service, profile,
                                                                                   rag_service_output, ):
        mock_dynamo_db_v2.get_conversation.return_value = MagicMock()
        mock_dynamo_db_v2.update_conversation_entry.return_value = MagicMock()
        mock_claims_explorer.return_value = ClaimsExplorerResult()
        expected_system_output = ClaimsExplorerResult(response="system response")
        expected_generate_answer = WestlawClaimsExplorerAnswer(rag_service_output=rag_service_output,
                                                               system_output=expected_system_output)

        actual_answer = await conversation_service.generate_answer(is_new_conversation=True, user_id=test_user_id,
                                                                   user_input=test_user_input,
                                                                   answer_solution_profile=profile.name,
                                                                   jurisdictions_override=[], content_types_override=[],
                                                                   conversation_id=test_conversation_id,
                                                                   conversation_entry_id=test_conversation_entry_id,
                                                                   conversation_action_type=ConversationActionType.RAG,
                                                                   auth_token=test_auth_token, answer_profile=profile,
                                                                   tags=[], worker_task=mock_worker_task,
                                                                   conversation_history=test_conversation_history,
                                                                   user_session={"sessionId": "test_session_id"},
                                                                   meta_data={"rag_version": "0.0.1",
                                                                              "app_version": "0.0.2",
                                                                              "python_version": "3.0.0",
                                                                              "conversation_type": "Initial",
                                                                              "check_intent": False, }, )

        assert expected_generate_answer == actual_answer
        assert not mock_check_intent.called
