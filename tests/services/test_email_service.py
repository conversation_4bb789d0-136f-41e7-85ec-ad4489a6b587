import pytest
from conversation_core.shared.enums import IntentClassification
from conversation_core.shared.models.answer_profile import AnswerProfile

test_email_address = "<EMAIL>"
test_destination_url = "https://destination.com"
test_sender = "sender"


class TestEmailService:
    @pytest.fixture
    def email_service(self):
        from services.email_service import EmailService

        return EmailService("ses_secret")

    @pytest.fixture
    def profile(self):
        return AnswerProfile(
            name="westlaw_claims_explorer_profile1",
            default_fermi_jurisdiction=["federal"],
            rag_solution="wl-claims-explorer-rag-v01",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"description": "",
                                   "additional_content_types": [],
                                   "include_additional_content_snippets": False,
                                   "max_entries_per_conversation": 6,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ["CASE", "STATUTE", "REGULATION", "KNOWHOW",
                                                                      "ANALYTICAL"],
                                   "intent_task_enabled": True,
                                   "use_llm_proxy": True,
                                   "llm_proxy_queue_name": None,
                                   "llm_proxy_subscription": "default",
                                   "llm_proxy_environment": "preprod",
                                   "llm_proxy_priority": 0,
                                   "llm_proxy_mock_enabled": False,
                                   "include_disclaimer": False,
                                   "llm_proxy_add_mock_indicator": False
                                   }

        )

    class TestGetEmailSubject:
        @pytest.mark.parametrize("success,expected_subject", [
            (True, "Your Claims Explorer search is ready"),
            (False, "Something went wrong when creating your Claims Explorer search")
        ])
        def test_get_email_subject(self, success, expected_subject, email_service, profile):
            assert expected_subject == email_service.get_email_subject(success=success, answer_profile=profile)

    class TestGenerateEmailBody:

        @pytest.mark.parametrize("success", [True, False])
        def test_generate_email_body_should_generate_empty_body_when_timestamp_is_invalid(self, success, email_service,
                                                                                          profile):
            actual_body = email_service.generate_email_body(timestamp=None, destination_url=test_destination_url,
                                                            success=success, answer_profile=profile)

            assert 0 == len(actual_body)

        def test_generate_email_body_should_generate_success_body_when_flag_is_true(self, email_service, profile):
            actual_body = email_service.generate_email_body(timestamp=0, destination_url=test_destination_url,
                                                            success=True, answer_profile=profile)

            assert test_destination_url in actual_body
            assert "The claims search you requested on 01/01 at 12:00 a.m. (UTC) is now ready." in actual_body
            assert "Return to Claims Explorer to view it." in actual_body

        def test_generate_email_body_should_generate_failure_body_when_flag_is_false(self, email_service, profile):
            actual_body = email_service.generate_email_body(timestamp=0, destination_url=test_destination_url,
                                                            success=False, answer_profile=profile)

            assert test_destination_url in actual_body
            assert "There was a problem performing the claims search you requested on 01/01 at 12:00 a.m. (UTC)." in actual_body
            assert "Please return to Claims Explorer to review it." in actual_body
