from unittest.mock import patch, Magic<PERSON>ock


@patch('services.answer_profile_service.AnswerProfileService')
@patch('services.answer_profile_service.get_entitlement_client')
def test_answer_profile_service(mock_get_entitlement_client, mock_answer_profile_service):
    settings = MagicMock()
    settings.GCS_URL = 'test_gcs_url'
    settings.GCS_USER_SECRET = 'test_secret'
    settings.RAS_CONFIG_BASE_URL = 'test_base_url'

    mock_get_entitlement_client.return_value = MagicMock()
    mock_answer_profile_service.return_value = MagicMock()

    mock_answer_profile_service(gcs_url=settings.GCS_URL, gcs_user_secret=settings.GCS_USER_SECRET,
                                ras_config_base_url=settings.RAS_CONFIG_BASE_URL,
                                entitlement_client=mock_get_entitlement_client(settings.GCS_URL))

    mock_get_entitlement_client.assert_called_once_with(settings.GCS_URL)
    mock_answer_profile_service.assert_called_once_with(gcs_url=settings.GCS_URL,
                                                        gcs_user_secret=settings.GCS_USER_SECRET,
                                                        ras_config_base_url=settings.RAS_CONFIG_BASE_URL,
                                                        entitlement_client=mock_get_entitlement_client.return_value)
