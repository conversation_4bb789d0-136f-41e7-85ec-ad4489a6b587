import warnings
from unittest.mock import patch

from pandas import DataFrame

from services.claims_data_service import load_claims, normalize_whitespace, normalize_title, normalize_cite, \
    ClaimsDataService

mocked_claims_data = DataFrame(
    {'ClaimsID': ['1', '2'], 'sourceLaw': ['law1', 'law2'], 'StatuteTitle': ['title1', 'title2'],
     '1stLineCitation': ['citation1', 'citation2'], 'subsection_cite': ['cite1', 'cite2'], 'sub_sort_order': ['1', '2'],
     'minimum_fact': ['fact1', 'fact2'], 'long_description': ['long_desc1', 'long_desc2'],
     'short_description': ['short_desc1', 'short_desc2'], })

mocked_alpha_id_to_uuid_map = {'ZX_1A1B': '1', 'ZX_2A2B': '2'}


class TestLoadClaims:
    @patch('app.utils.common_util.read_csv_to_json')
    def test_load_claims(self, mock_read_csv):
        mock_claims = [
            {'ClaimsID': str(i), 'sourceLaw': f'law{i}', 'StatuteTitle': f'title{i}', '1stLineCitation': f'citation{i}',
             'subsection_cite': f'cite{i}', 'sub_sort_order': str(i), 'minimum_fact': f'fact{i}'} for i in
            range(1, 7326)]
        mock_read_csv.return_value = mock_claims

        claims_df, alpha_id_to_uuid_map = load_claims()

        assert isinstance(claims_df, DataFrame)
        assert isinstance(alpha_id_to_uuid_map, dict)
        assert len(claims_df) == 15123
        assert len(alpha_id_to_uuid_map) == 15123

    @patch('app.utils.common_util.read_csv_to_json')
    def test_load_claims_with_missing_data(self, mock_read_csv):
        mock_read_csv.return_value = [
            {'ClaimsID': '', 'sourceLaw': '', 'StatuteTitle': 'title1', '1stLineCitation': 'citation1',
             'subsection_cite': 'cite1', 'sub_sort_order': '', 'minimum_fact': ''},
            {'ClaimsID': '2', 'sourceLaw': 'common law', 'StatuteTitle': 'title2', '1stLineCitation': '',
             'subsection_cite': '', 'sub_sort_order': '2', 'minimum_fact': 'fact2'}]

        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            claims_df, alpha_id_to_uuid_map = load_claims()

        assert isinstance(claims_df, DataFrame)
        assert isinstance(alpha_id_to_uuid_map, dict)
        assert len(claims_df) == 15123
        assert len(alpha_id_to_uuid_map) == 15123


class TestNormalizationFunctions:
    def test_normalize_whitespace(self):
        assert normalize_whitespace("Hello\u00A0World") == "Hello World"
        assert normalize_whitespace("Hello\u2009World") == "HelloWorld"
        assert normalize_whitespace("Hello World") == "Hello World"

    def test_normalize_title(self):
        assert normalize_title("§ 1234") == "§ 1234"
        assert normalize_title("S 1234") == "§ 1234"
        assert normalize_title("Section 1234") == "Section 1234"
        assert normalize_title("S. 1234") == "§ 1234"
        assert normalize_title("Regular Title") == "Regular Title"

    def test_normalize_cite(self):
        assert normalize_cite("1 U.S.C.A. 1234") == "1 U.S.C.A. 1234"
        assert normalize_cite("1 U.S.C. 1234") == "1 U.S.C.A. 1234"
        assert normalize_cite("1 USCA 1234") == "1 U.S.C.A. 1234"
        assert normalize_cite("1 USC 1234") == "1 U.S.C.A. 1234"
        assert normalize_cite("Title 1 Section 1234") == "Title 1 Section 1234"
        assert normalize_cite("Title 1 S. 1234") == "Title 1 § 1234"
        assert normalize_cite("Regular Citation") == "Regular Citation"


class TestClaimsDataService:
    @classmethod
    def setup_class(cls):
        cls.claims_data_service = ClaimsDataService()
        cls.claims_data_service.claims_data = mocked_claims_data
        cls.claims_data_service.alpha_id_to_uuid_map = mocked_alpha_id_to_uuid_map

    def test_get_all_records(self):
        records = self.claims_data_service.get_all_records()
        assert len(records) == 2
        assert records[0]['ClaimsID'] == '1'

    def test_get_records_by_claims_id(self):
        records = self.claims_data_service.get_records_by_claims_id('1')
        assert len(records) == 1
        assert records[0]['ClaimsID'] == '1'

    def test_get_claims_id_from_alpha_id(self):
        claims_id = self.claims_data_service.get_claims_id_from_alpha_id('ZX_1A1B')
        assert claims_id == '1'

    def test_get_source_law_by_claim_id(self):
        source_law = self.claims_data_service.get_source_law_by_claim_id('1')
        assert source_law == 'law1'

    def test_get_jurisdiction_by_claim_id(self):
        self.claims_data_service.claims_data['Jurisdiction'] = ['jurisdiction1', 'jurisdiction2']
        jurisdiction = self.claims_data_service.get_jurisdiction_by_claim_id('1')
        assert jurisdiction == 'jurisdiction1'

    def test_get_distinct_minimum_facts(self):
        min_facts = self.claims_data_service.get_distinct_minimum_facts()
        assert len(min_facts) == 2
        assert 'fact1' in min_facts

    def test_get_distinct_minimum_facts_for_jurisdictions(self):
        self.claims_data_service.claims_data['Jurisdiction'] = ['jurisdiction1', 'jurisdiction2']
        min_facts = self.claims_data_service.get_distinct_minimum_facts_for_jurisdictions(['jurisdiction1'])
        assert len(min_facts) == 1
        assert 'fact1' in min_facts

    def test_get_statutes_by_jurisdiction(self):
        self.claims_data_service.claims_data['Jurisdiction'] = ['jurisdiction1', 'jurisdiction2']
        self.claims_data_service.claims_data['actionable'] = ['Y', 'R']

        statutes = self.claims_data_service.get_statutes_by_jurisdiction(['fact1'], 'jurisdiction1')
        assert len(statutes) == 1
        assert statutes[0]['ClaimsID'] == '1'

    def test_get_statutes_by_jurisdiction_none(self):
        self.claims_data_service.claims_data['Jurisdiction'] = ['jurisdiction1', 'jurisdiction2']
        self.claims_data_service.claims_data['actionable'] = ['Y', 'R']
        self.claims_data_service.claims_data['minimum_fact'] = ['none', 'fact2']

        statutes = self.claims_data_service.get_statutes_by_jurisdiction(["None", "None"], 'jurisdiction1')
        assert len(statutes) == 1
        assert statutes[0]['ClaimsID'] == '1'

    def test_query_statutes_by_jurisdiction(self):
        self.claims_data_service.claims_data['Jurisdiction'] = ['jurisdiction1', 'jurisdiction2']
        self.claims_data_service.claims_data['actionable'] = ['Y', 'R']
        self.claims_data_service.claims_data['minimum_fact'] = ['fact1', 'fact2']

        statutes = self.claims_data_service.query_statutes_by_jurisdiction(
            desired_columns=["ClaimsID", "StatuteTitle", "subsection_cite", "long_description", "short_description"],
            jurisdiction='jurisdiction1', minimum_facts=['fact1'], actionable_values=['Y', 'R', 'U'])

        assert len(statutes) == 1
        assert statutes[0]['ClaimsID'] == '1'
        assert 'Title' in statutes[0]
        assert 'Citation' in statutes[0]
        assert 'Long Desc' in statutes[0]
        assert 'Short Desc' in statutes[0]
