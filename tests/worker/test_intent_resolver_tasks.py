from unittest.mock import patch, ANY

from worker.intent_resolver_tasks import IntentResolverTask, IntentResolverTaskImpl, settings


class TestIntentResolverTasks:

    @patch('worker.intent_resolver_tasks.IntentResolverTaskImpl')
    def test_evaluate_intent_task(self, mock_intent_resolver_task_impl):
        task = IntentResolverTask()
        mock_impl = mock_intent_resolver_task_impl.return_value

        test_params = {"is_new_conversation": True, "user_id": "user123", "user_input": "Hello",
                       "answer_solution_profile": "profile1", "jurisdictions_override": ["US"],
                       "content_types_override": ["text"], "conversation_id": "conv123",
                       "conversation_entry_id": "entry123", "conversation_action_type": "action1",
                       "auth_token": "token123", "user_session": {"key": "value"}, "meta_data": {"meta": "data"}}

        result = task.evaluate_intent_task(**test_params)

        mock_impl.evaluate_intent_task.assert_called_once_with(task=ANY, **test_params)
        assert result == mock_impl.evaluate_intent_task.return_value

    @patch('worker.intent_resolver_tasks.IntentResolverTaskBaseV3.__init__', return_value=None)
    @patch('worker.intent_resolver_tasks.entitlement_client')
    @patch('worker.intent_resolver_tasks.intent_resolver_service')
    def test_intent_resolver_task_impl_initialization(self, mock_intent_resolver_service, mock_entitlement_client,
                                                      mock_init):
        IntentResolverTaskImpl()
        mock_init.assert_called_once_with(settings=settings, intent_resolver_service=mock_intent_resolver_service,
                                          rag_service_wheel_name="labs-aalp-service",
                                          entitlement_client=mock_entitlement_client)

    def test_shared_task_decorator(self):
        task = IntentResolverTask()
        assert hasattr(task.evaluate_intent_task, 'delay')
        assert task.evaluate_intent_task.name == "evaluate_intent"
        assert task.evaluate_intent_task.retry_backoff is True
        assert task.evaluate_intent_task.retry_kwargs == {"max_retries": 1}

    def test_class_inheritance(self):
        from conversation_core.shared.tasks.task_contracts import IntentResolverTasks
        from conversation_core.shared.tasks.intent_resolver_tasks_base import IntentResolverTaskBaseV3
        assert issubclass(IntentResolverTaskImpl, IntentResolverTaskBaseV3)
        assert issubclass(IntentResolverTask, IntentResolverTasks)
