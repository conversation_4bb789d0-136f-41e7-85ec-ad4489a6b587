from unittest.mock import patch, <PERSON><PERSON>

import pytest

from worker.conversation_tasks import ConversationTaskV2, ConversationTaskImpl, finish_task, settings, \
    entitlement_client, conversation_service, dynamo_db_v2


@pytest.fixture
def mock_conversation_task_impl():
    with patch('worker.conversation_tasks.ConversationTaskImpl') as mock:
        yield mock


@pytest.fixture
def conversation_task():
    return ConversationTaskV2()


@pytest.fixture
def mock_span():
    return Mock()


class TestConversationTaskV2:
    @pytest.fixture(autouse=True)
    def setup_method(self, mock_conversation_task_impl):
        mock_conversation_task_impl.reset_mock()


def test_shared_task_decorator(conversation_task):
    assert hasattr(conversation_task.start_conversation_task, 'delay')
    assert conversation_task.start_conversation_task.name == 'start_conversation_v2'
    assert conversation_task.start_conversation_task.retry_backoff is True
    assert conversation_task.start_conversation_task.retry_kwargs == {"max_retries": 1}


class TestConversationTaskImpl:
    def test_conversation_task_impl_init(self):
        with patch('worker.conversation_tasks.ConversationTaskBaseV2.__init__') as mock_init:
            impl = ConversationTaskImpl()

            mock_init.assert_called_once_with(settings=settings, dynamo_db_v2=dynamo_db_v2,
                                              entitlement_client=entitlement_client,
                                              conversation_service=conversation_service,
                                              rag_service_wheel_name="labs-aalp-service")


class TestFinishTask:
    @patch('worker.conversation_tasks.send_metrics')
    @patch('worker.conversation_tasks.add_to_span')
    @patch('worker.conversation_tasks.sys.exc_info')
    def test_finish_task(self, mock_exc_info, mock_add_to_span, mock_send_metrics, mock_span):
        conversation_id = "conv123"
        conversation_entry_id = "entry123"
        answer_solution_profile = "test_profile"
        total_queued_time = 1.0
        total_execution_time = 2.0
        response_status_code = 200
        user_classification = "test_user"
        mock_exc_info.return_value = (None, None, None)

        finish_task(mock_span, conversation_id, conversation_entry_id, answer_solution_profile, total_queued_time,
                    total_execution_time, response_status_code, user_classification)

        mock_send_metrics.assert_called_once_with(conversation_id=conversation_id,
                                                  conversation_entry_id=conversation_entry_id,
                                                  answer_solution_profile=answer_solution_profile,
                                                  total_queued_time=total_queued_time,
                                                  total_execution_time=total_execution_time, success=False,
                                                  user_classification=user_classification)
        mock_add_to_span.assert_called_once_with('http.status_code', f'{response_status_code}')
        mock_span.set_exc_info.assert_called_once_with(None, None, None)
