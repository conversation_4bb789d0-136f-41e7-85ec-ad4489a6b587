from datetime import datetime
from unittest.mock import Mock

import pytest
from aalp_service.v2.intent_resolver.dataclasses import AnnotationType
from conversation_core.shared.enums import IntentClassification, RetrieveConversationEntryStatuses, \
    ConversationActionType

from utils.converters import convert_conversation_entries_to_conversation_entries, \
    convert_annotation_to_intent_classification


class TestConvertConversationEntries:

    @pytest.fixture
    def mock_conversation_entry(self):
        return Mock(status=RetrieveConversationEntryStatuses.COMPLETE,
                    result=Mock(conversation_action_type=ConversationActionType.RAG, system_output={
                        "rag_pipeline_output": {"intent": {"user_input": {"content_types": ["text"]}},
                                                "user_input": {"content_types": ["text"]}}}, intermediate_results=None),
                    timestamp=datetime(2023, 1, 1, 10, 0))

    @pytest.mark.parametrize("rag_model,intent_model,expected_length",
                             [("test_rag", None, 1), (None, "test_intent", 0), ("test_rag", "test_intent", 1),
                              (None, None, 0)])
    def test_convert_conversation_entries(self, mock_conversation_entry, rag_model, intent_model, expected_length):
        result = convert_conversation_entries_to_conversation_entries([mock_conversation_entry], rag_model=rag_model,
                                                                      intent_model=intent_model)
        assert len(result) == expected_length
        if expected_length > 0:
            assert result[0]["type"] == rag_model

    def test_convert_conversation_entries_empty_history(self):
        result = convert_conversation_entries_to_conversation_entries([])
        assert result == []

    def test_convert_conversation_entries_incomplete_entry(self, mock_conversation_entry):
        mock_conversation_entry.status = RetrieveConversationEntryStatuses.IN_PROGRESS
        result = convert_conversation_entries_to_conversation_entries([mock_conversation_entry], rag_model="test_rag")
        assert len(result) == 0

    def test_convert_conversation_entries_sorting(self):
        entries = [Mock(status=RetrieveConversationEntryStatuses.COMPLETE,
                        result=Mock(conversation_action_type=ConversationActionType.RAG, system_output={
                            "rag_pipeline_output": {"intent": {"user_input": {"content_types": ["text"]}},
                                                    "user_input": {"content_types": ["text"]}}}),
                        timestamp=datetime(2023, 1, 1, 10, 2)), Mock(status=RetrieveConversationEntryStatuses.COMPLETE,
                                                                     result=Mock(
                                                                         conversation_action_type=ConversationActionType.INTENT,
                                                                         system_output={"intent": "some_intent"}),
                                                                     timestamp=datetime(2023, 1, 1, 10, 1))]
        result = convert_conversation_entries_to_conversation_entries(entries, rag_model="test_rag",
                                                                      intent_model="test_intent")
        assert len(result) == 2
        assert result[0]["type"] == "test_intent"
        assert result[1]["type"] == "test_rag"


class TestConvertAnnotationToIntentClassification:

    @pytest.mark.parametrize("annotation_name,expected", [("FIND_BY_CITATION", IntentClassification.FIND_BY_CITATION),
                                                          ("KEY_ISSUE", IntentClassification.KEY_ISSUE),
                                                          ("NON_LEGAL", IntentClassification.NON_LEGAL),
                                                          ("LEGAL", IntentClassification.LEGAL),
                                                          ("INCOMPLETE", IntentClassification.INCOMPLETE), (
                                                                  "ILLEGAL_INFORMATION",
                                                                  IntentClassification.ILLEGAL_INFORMATION),
                                                          ("OUTCOMES", IntentClassification.OUTCOMES),
                                                          ("LEGAL_MATH", IntentClassification.LEGAL_MATH),
                                                          ("ADVERSARIAL", IntentClassification.ADVERSARIAL),
                                                          ("BOOLEAN_QUERY", IntentClassification.BOOLEAN_QUERY), (
                                                                  "CHANGE_JURISDICTION",
                                                                  IntentClassification.CHANGE_JURISDICTION), (
                                                                  "FOLLOW_UP_SPECIFIC_DOC",
                                                                  IntentClassification.FOLLOW_UP_SPECIFIC_DOC), (
                                                                  "REQUEST_TO_SUMMARIZE",
                                                                  IntentClassification.REQUEST_TO_SUMMARIZE)])
    def test_convert_annotation(self, annotation_name, expected):
        annotation = AnnotationType(annotation_name)
        result = convert_annotation_to_intent_classification(annotation)
        assert result == expected

    def test_convert_annotation_case_sensitivity(self):
        annotation_lower = AnnotationType("find_by_citation")
        annotation_upper = AnnotationType("FIND_BY_CITATION")
        assert convert_annotation_to_intent_classification(
            annotation_lower) == convert_annotation_to_intent_classification(annotation_upper)

    def test_convert_annotation_with_none(self):
        with pytest.raises(AttributeError):
            convert_annotation_to_intent_classification(None)
