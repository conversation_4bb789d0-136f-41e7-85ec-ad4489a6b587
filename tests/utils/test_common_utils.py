import duckdb
import pytest
from pandas import DataFrame

from app.utils.common_util import (read_csv_to_json, dicts_to_dataframe, mock_randomize_categorization_options,
                                   mock_randomize_filter_responses, mock_randomize_answer_responses,
                                   mock_randomize_answer_responses_v2)


class TestCommonUtil:
    csv_expected_results: list = [(0, [('ClaimsID', 'cb493585-8161-4165-9fb6-aa607a11c68f'), (
        'StatuteTitle', 'S987. Terms of consumer credit extended to members and dependents: limitations'),
                                       ('Jurisdiction', 'Federal'), ('minimum_fact',
                                                                     'involves a loan;involves interest;involves a military servicemember'),
                                       ('docGUID', 'N188E2710F90911E6AF808584347ED4CA'),
                                       ('PopularNames', 'Military Lending Act'), ('actionable', 'Y')]), (15, [
        ('ClaimsID', '9dfc2639-4cae-4a7b-a588-deee47b60903'),
        ('StatuteTitle', 'S1786.40. Consumer insurance request denied; notice to consumer of adverse action'),
        ('PopularNames', 'NA'), ('minimum_fact',
                                 'involves an insurer;involves an insured;involves insurance;involves a communication or failure to communicate;involves a disclosure;involves a credit bureau;involves a credit report'),
        ('docGUID', 'NFD03FC408E5911D8A8ACD145B11214D7')]), (28, [('ClaimsID', '37e458e3-c758-4395-b726-39d695993b10'),
                                                                  ('StatuteTitle',
                                                                   'Public Policy Violation - Disability Discrimination'),
                                                                  ('1stLineCitation', 'None'), ('actionable', 'N'),
                                                                  ('minimum_fact', 'none'), ('docGUID', None)]), (29, [
        ('ClaimsID', None), ('StatuteTitle', 'Amendment I. Freedom of association clause'), ('actionable', 'Y'),
        ('PopularNames', 'NA'), ('docGUID', 'N9EB9EF409DFA11D8A63DAA9EBCE8FE5A')]), (34, [
        ('ClaimsID', '5eb6b1c1-a17a-43b5-9993-bc7d2fa6853f'),
        ('StatuteTitle', "S1667b. Lessee's liability on expiration or termination of lease"),
        ('docGUID', 'NB43B07A0AFF711D8803AE0632FEDDFBF')])]

    def test_read_csv_to_json(self):
        result = read_csv_to_json('tests/test_resources/ClaimEx_Worksheet_TEST.csv')

        assert len(result) == 35

        for entry in self.csv_expected_results:
            entry_num = entry[0]
            values_to_test = entry[1]
            result_dict = result[entry_num]
            for test_pair in values_to_test:
                key = test_pair[0]
                expected_value = test_pair[1]
                actual_value = result_dict[key]
                assert (expected_value == actual_value)

    def test_convert_dict_to_dataframe(self):
        test_dicts = []
        dict_1 = {"Col1": "TC1R1", "Col2": "TC2R1", "Col3": None}
        dict_2 = {"Col1": "TC1R2", "Col2": None, "Col3": "TC3R2"}
        dict_3 = {"Col1": None, "Col2": "TC2R3", "Col3": "TC3R3"}
        test_dicts.append(dict_1)
        test_dicts.append(dict_2)
        test_dicts.append(dict_3)

        result: DataFrame = dicts_to_dataframe(test_dicts)

        assert result.columns.values.tolist() == ["Col1", "Col2", "Col3"]

        query_1_result = duckdb.query(
            "select Col1 from result where Col1 is not null").df().to_numpy().flatten().tolist()
        query_2_result = duckdb.query(
            "select Col2 from result where Col2 is not null").df().to_numpy().flatten().tolist()
        query_3_result = duckdb.query(
            "select Col3 from result where Col3 is not null").df().to_numpy().flatten().tolist()

        assert query_1_result == ["TC1R1", "TC1R2"]
        assert query_2_result == ["TC2R1", "TC2R3"]
        assert query_3_result == ["TC3R2", "TC3R3"]

    def test_read_csv_to_json_file_not_found(self):
        result = read_csv_to_json('non_existent_file.csv')
        assert result == []

    def test_read_csv_to_json_empty_file(self, tmp_path):
        empty_file = tmp_path / "empty_file.csv"
        empty_file.touch()
        result = read_csv_to_json(str(empty_file))
        assert len(result) == 0

    def test_dicts_to_dataframe_empty_list(self):
        result = dicts_to_dataframe([])
        assert result.empty

    def test_dicts_to_dataframe_single_dict(self):
        test_dict = {"Col1": "Value1", "Col2": "Value2"}
        result = dicts_to_dataframe([test_dict])
        assert result.shape == (1, 2)
        assert result.iloc[0].to_dict() == test_dict

    def test_dicts_to_dataframe_mixed_types(self):
        test_dicts = [{"Col1": 1, "Col2": "A", "Col3": True}, {"Col1": 2.5, "Col2": "B", "Col3": False}]
        result = dicts_to_dataframe(test_dicts)
        assert result.shape == (2, 3)
        assert result.dtypes['Col1'] == 'float64'
        assert result.dtypes['Col2'] == 'object'
        assert result.dtypes['Col3'] == 'bool'

    def test_dicts_to_dataframe_exception(self):
        with pytest.raises(Exception):
            dicts_to_dataframe("Not a list of dicts")

    def test_mock_randomize_categorization_options(self):
        input_string = "option1\\noption2\\noption3\\noption4\\noption5"
        result = mock_randomize_categorization_options(input_string)
        assert isinstance(result, list)
        assert all(item in input_string.split("\\n") for item in result)

    def test_mock_randomize_filter_responses(self):
        input_list = [{'ClaimsID': '1'}, {'ClaimsID': '2'}, {'ClaimsID': '3'}]
        result = mock_randomize_filter_responses(input_list)
        assert len(result) == len(input_list)
        assert all('ClaimsID' in item and 'Answer' in item for item in result)
        assert all(item['Answer'] in ['Yes', 'No', 'Possibly'] for item in result)

    def test_mock_randomize_answer_responses(self):
        input_string = "{'ClaimsID': '12345'}"
        result = mock_randomize_answer_responses(input_string)
        assert 'ClaimsID' in result
        assert result['ClaimsID'] == '12345'
        assert 'Answer' in result
        assert result['Answer'] in ['Yes', 'No', 'Possibly with Additional Fact']
        assert 'Discussion' in result

    def test_mock_randomize_answer_responses_v2(self):
        input_dicts: list[dict] = [{"ClaimsID": "12345"}, {"ClaimsID": "67890"}]
        result: list[dict] = mock_randomize_answer_responses_v2(input_dicts)
        i = 0
        for input_dict in input_dicts:
            result_dict = result[i]
            assert 'ClaimsID' in result_dict
            assert result_dict['ClaimsID'] == input_dict['ClaimsID']
            assert 'answer' in result_dict
            assert result_dict['answer'] in ['Yes', 'No', 'Possibly with Additional Fact']
            assert 'thoughts' in result_dict
            i += 1