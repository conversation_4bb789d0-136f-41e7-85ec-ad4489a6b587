from app.utils.sorting.sort_util import do_sort

input_docs_stat = [{"claim_id": "1a", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 77l", "citation": "15 U.S.C.A.  77l(a)(2)",
                    "sub_sort_order": "2", "jurisdictions": ["Federal"]},
                   {"claim_id": "2b", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 78t-1", "citation": "15 U.S.C.A. 78t-1",
                    "sub_sort_order": "1",
                    "jurisdictions": ["Federal"]},
                   {"claim_id": "3c", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 1681c-1", "citation": "15 U.S.C.A. 1681c-1",
                    "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                   {"claim_id": "4d", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 1703", "citation": "15 U.S.C.A. 1703(a)(1)(A)",
                    "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                   {"claim_id": "5e", "answer": "Yes", "1st_line_citation": "10 U.S.C.A. 987", "citation": "10 U.S.C.A. 987(a)",
                    "sub_sort_order": "1",
                    "jurisdictions": ["Federal"]},
                   {"claim_id": "6f", "answer": "Yes", "1st_line_citation": "10 U.S.C.A. 987", "citation": "10 U.S.C.A. 987(d)(2)",
                    "sub_sort_order": "4", "jurisdictions": ["Federal"]},
                   {"claim_id": "7a", "answer": "Yes", "1st_line_citation": "12 U.S.C.A. 2605", "citation": "12 U.S.C.A. 2605(k)(1)(A)",
                    "sub_sort_order": "7", "jurisdictions": ["Federal"]},
                   {"claim_id": "8b", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 1703", "citation": "15 U.S.C.A. 1703(e)",
                    "sub_sort_order": "12", "jurisdictions": ["Federal"]},
                   {"claim_id": "9c", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 77l", "citation": "15 U.S.C.A.  77l(a)(1)",
                    "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                   {"claim_id": "10d", "answer": "Yes", "1st_line_citation": "CA CIVIL § 1689.14", "citation": "CA CIVIL § 1689.14",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "11e", "answer": "Yes", "1st_line_citation": "West's Ann.Cal.Civ.Code § 1798.41",
                    "citation": "West's Ann.Cal.Civ.Code § 1798.41(a)", "sub_sort_order": "1",
                    "jurisdictions": ["California"]},
                   {"claim_id": "12f", "answer": "Yes", "1st_line_citation": "West's Ann.Cal.Civ.Code § 1798.41",
                    "citation": "West's Ann.Cal.Civ.Code § 1798.41(c) ", "sub_sort_order": "3",
                    "jurisdictions": ["California"]},
                   {"claim_id": "13a", "answer": "Yes", "1st_line_citation": "CA CIVIL § 841.4",
                    "citation": "West's Ann.Cal.Civ.Code § 841.4",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "14b", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 7071.11",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 7071.11", "sub_sort_order": "1",
                    "jurisdictions": ["California"]},
                   {"claim_id": "15c", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 7071.4",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 7071.4", "sub_sort_order": "1",
                    "jurisdictions": ["California"]},
                   {"claim_id": "16d", "answer": "Yes", "1st_line_citation": "CA CIV PRO § 760.020",
                    "citation": "West's Ann.Cal.C.C.P. § 760.020(a)",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "17e", "answer": "Yes", "1st_line_citation": "CA CIV PRO § 760.020",
                    "citation": "West's Ann.Cal.C.C.P. § 760.020(b)",
                    "sub_sort_order": "2", "jurisdictions": ["California"]},
                   {"claim_id": "18f", "answer": "Yes", "1st_line_citation": "CA CORP § 1507",
                    "citation": "West's Ann.Cal.Corp.Code § 1507(b)",
                    "sub_sort_order": "2", "jurisdictions": ["California"]},
                   {"claim_id": "19a", "answer": "Yes", "1st_line_citation": "17 U.S.C.A. 106A",
                    "citation": "17 U.S.C.A. 106A (a) (1) (B)",
                    "sub_sort_order": "2", "jurisdictions": ["Federal"]},
                   {"claim_id": "20b", "answer": "Yes", "1st_line_citation": "CA CORP § 304",
                    "citation": "West's Ann.Cal.Corp.Code § 304",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "21c", "answer": "Yes", "1st_line_citation": "CA CIVIL § 1090.5",
                    "citation": "West's Ann.Cal.Civ.Code § 1090.5(a)(3)",
                    "sub_sort_order": "3", "jurisdictions": ["California"]},
                   {"claim_id": "22d", "answer": "Yes", "1st_line_citation": "CA CIVIL § 1090.5",
                    "citation": "West's Ann.Cal.Civ.Code § 1090.5(a)(5)",
                    "sub_sort_order": "5", "jurisdictions": ["California"]},
                   {"claim_id": "23e", "answer": "Yes", "1st_line_citation": "CA COML § 3411",
                    "citation": "West's Ann.Cal.Const. Art. 1, § 13",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "24f", "answer": "Yes", "1st_line_citation": "CA CONST Art. 1, § 13",
                    "citation": "West's Ann.Cal.Const. Art. 1, § 13",
                    "sub_sort_order": "5", "jurisdictions": ["California"]},
                   {"claim_id": "25a", "answer": "Yes", "1st_line_citation": "CA CONST Art. 1, § 13",
                    "citation": "West's Ann.Cal.Const. Art. 1, § 13",
                    "sub_sort_order": "7", "jurisdictions": ["California"]},
                   {"claim_id": "26b", "answer": "Yes", "1st_line_citation": "10 U.S.C.A. 987", "citation": "10 U.S.C.A. 987(e)(1)",
                    "sub_sort_order": "5", "jurisdictions": ["Federal"]},
                   {"claim_id": "27c", "answer": "Yes", "1st_line_citation": "12 U.S.C.A. 1701x", "citation": "12 U.S.C.A. 1701x",
                    "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                   {"claim_id": "28d", "answer": "Yes", "1st_line_citation": "CA INS § 1871.7",
                    "citation": "West's Ann.Cal.Ins.Code § 1871.7",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "29e", "answer": "Yes", "1st_line_citation": "CA INS § 1871.7",
                    "citation": "West's Ann.Cal.Ins.Code § 1871.7(e)(1)",
                    "sub_sort_order": "3", "jurisdictions": ["California"]},
                   {"claim_id": "30f", "answer": "Yes", "1st_line_citation": "12 U.S.C.A. 2605", "citation": "12 U.S.C.A. 2605(k)(1)(B)",
                    "sub_sort_order": "8", "jurisdictions": ["Federal"]},
                   {"claim_id": "31a", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 1", "citation": "15 U.S.C.A. 1",
                    "sub_sort_order": "1",
                    "jurisdictions": ["Federal"]},
                   {"claim_id": "32b", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 1064", "citation": "15 U.S.C.A. 1064",
                    "sub_sort_order": "1",
                    "jurisdictions": ["Federal"]},
                   {"claim_id": "33c", "answer": "Yes", "1st_line_citation": "15 U.S.C.A. 1064", "citation": "15 U.S.C.A. 1064(3)",
                    "sub_sort_order": "2", "jurisdictions": ["Federal"]},
                   {"claim_id": "53e", "answer": "Yes", "1st_line_citation": "CA GOVT § 12921",
                    "citation": "§ 12921. Civil rights; employment and housing without discrimination",
                    "sub_sort_order": "8", "jurisdictions": ["California"]},
                   {"claim_id": "34d", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 10241",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 10241", "sub_sort_order": "1",
                    "jurisdictions": ["California"]},
                   {"claim_id": "35e", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 14205",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 14205(e)(1)", "sub_sort_order": "5",
                    "jurisdictions": ["California"]},
                   {"claim_id": "36f", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 17577.2",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 17577.2(b)",
                    "sub_sort_order": "2", "jurisdictions": ["California"]},
                   {"claim_id": "37a", "answer": "Yes", "1st_line_citation": "CA EDUC § 221.8",
                    "citation": "West's Ann.Cal.Educ.Code § 221.8(c)",
                    "sub_sort_order": "3", "jurisdictions": ["California"]},
                   {"claim_id": "38b", "answer": "Yes", "1st_line_citation": "CA EDUC § 221.8",
                    "citation": "West's Ann.Cal.Educ.Code § 221.8(f)",
                    "sub_sort_order": "6", "jurisdictions": ["California"]},
                   {"claim_id": "39c", "answer": "Yes", "1st_line_citation": "CA FISH & G § 5650",
                    "citation": "West's Ann.Cal.Fish & G.Code § 5650",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "40d", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 17577.2",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 17577.2(d)", "sub_sort_order": "4",
                    "jurisdictions": ["California"]},
                   {"claim_id": "41e", "answer": "Yes", "1st_line_citation": "CA CORP § 1507",
                    "citation": "West's Ann.Cal.Corp.Code § 1507(c)",
                    "sub_sort_order": "3", "jurisdictions": ["California"]},
                   {"claim_id": "42f", "answer": "Yes", "1st_line_citation": "CA STR & HWY § 5611",
                    "citation": "West's Ann.Cal.Str. & H.Code § 5611",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "43a", "answer": "Yes", "1st_line_citation": "17 U.S.C.A. 106A", "citation": "17 U.S.C.A. 106A (a) (2)",
                    "sub_sort_order": "3", "jurisdictions": ["Federal"]},
                   {"claim_id": "44b", "answer": "Yes", "1st_line_citation": "17 U.S.C.A. 1101", "citation": "17 U.S.C.A. 1101(a)(2)",
                    "sub_sort_order": "2", "jurisdictions": ["Federal"]},
                   {"claim_id": "52d", "answer": "Yes", "1st_line_citation": "CA GOVT § 11135",
                    "citation": "§ 11135. Programs or activities funded by state; discrimination on basis of sex, race, color, religion, ancestry, national origin, ethnic group identification, age, mental disability, physical disability, medical condition, genetic information, marital status, or sexual orientation; federal act; definitions; legislative findings and declarations",
                    "sub_sort_order": "1", "jurisdictions": ["California"]},
                   {"claim_id": "45c", "answer": "Yes", "1st_line_citation": "49 U.S.C.A. 32703", "citation": "49 U.S.C.A. 32703(a)(4)",
                    "sub_sort_order": "4", "jurisdictions": ["Federal"]},
                   {"claim_id": "46d", "answer": "Yes", "1st_line_citation": "5 U.S.C.A. 2302", "citation": "5 U.S.C.A. 2302(b)(1)",
                    "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                   {"claim_id": "47e", "answer": "Yes", "1st_line_citation": "7 U.S.C.A. 2", "citation": "7 U.S.C.A. 2(a)(1)(D)(vii)",
                    "sub_sort_order": "3", "jurisdictions": ["Federal"]},
                   {"claim_id": "48f", "answer": "Yes", "1st_line_citation": "7 U.S.C.A. 2", "citation": "7 U.S.C.A. 2(a)(1)(D)(i)(IV)",
                    "sub_sort_order": "7", "jurisdictions": ["Federal"]},
                   {"claim_id": "49a", "answer": "Yes", "1st_line_citation": "7 U.S.C.A. 2", "citation": "7 U.S.C.A. 2(e)",
                    "sub_sort_order": "15",
                    "jurisdictions": ["Federal"]}, {"claim_id": "50b", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 10241.3",
                                                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 10241.3",
                                                    "sub_sort_order": "1",
                                                    "jurisdictions": ["California"]},
                   {"claim_id": "51c", "answer": "Yes", "1st_line_citation": "CA BUS & PROF § 14205",
                    "citation": "West's Ann.Cal.Bus. & Prof.Code § 14205(f)", "sub_sort_order": "8",
                    "jurisdictions": ["California"]}]

expected_sort_order_stat = ["46d", "47e", "48f", "49a", "5e", "6f", "26b", "27c", "7a", "30f", "31a", "9c", "1a", "2b",
                            "32b", "33c", "3c", "4d", "8b", "19a", "43a", "44b", "45c", "15c", "14b", "34d", "50b",
                            "35e", "51c", "36f", "40d",
                            "16d", "17e", "13a", "21c", "22d", "10d", "23e", "24f", "25a", "20b", "18f", "41e", "37a",
                            "38b", "39c", "52d",
                            "53e", "28d", "29e", "42f", "11e", "12f"]


def test_sort_statute_fed_cali_mix():
    sorted_results = do_sort(source_law="statutes", entries=input_docs_stat, is_mock_run=False)
    i = 0
    for result in sorted_results:
        assert result['claim_id'] == expected_sort_order_stat[i]
        i += 1


input_docs_comlaw = [{"claim_id": "8b", "answer": "Yes", "title": "Breach of Warranty - Implied Warranty", "sub_sort_order": "32",
                      "jurisdictions": ["California"]},
                     {"claim_id": "14b", "answer": "Yes", "title": "Void/Voidable Contract - Rescission - Illegality",
                      "sub_sort_order": "138",
                      "jurisdictions": ["California"]},
                     {"claim_id": "9c", "answer": "Yes", "title": "Estoppel - Promissory Estoppel", "sub_sort_order": "49",
                      "jurisdictions": ["California"]},
                     {"claim_id": "10d", "answer": "Yes", "title": "Invasion of Privacy - Appropriation of Name or Likeness",
                      "sub_sort_order": "74",
                      "jurisdictions": ["California"]},
                     {"claim_id": "13a", "answer": "Yes", "title": "Account Stated", "sub_sort_order": "148",
                      "jurisdictions": ["California"]},
                     {"claim_id": "12f", "answer": "Yes", "title": "\"Waste\" by a corporate officer/board", "sub_sort_order": "150",
                      "jurisdictions": ["California"]},
                     {"claim_id": "7a", "answer": "Yes", "title": "Insurance - Bad Faith", "sub_sort_order": "14",
                      "jurisdictions": ["California"]},
                     {"claim_id": "15c", "answer": "Yes", "title": "Void/Voidable Contract - Rescission - Incapacity",
                      "sub_sort_order": "139",
                      "jurisdictions": ["California"]},
                     {"claim_id": "11e", "answer": "Yes", "title": "Void/Voidable Contract - Rescission - Duress",
                      "sub_sort_order": "136",
                      "jurisdictions": ["California"]}]

expected_sort_order_comlaw = ["13a", "8b", "9c", "7a", "10d", "11e", "14b", "15c", "12f"]


def test_sort_comlaw_fed_cali_mix():
    sorted_results = do_sort(source_law="common_law", entries=input_docs_comlaw, is_mock_run=False)
    i = 0
    for result in sorted_results:
        assert result['claim_id'] == expected_sort_order_comlaw[i]
        i += 1


input_docs_constit = [
    {"claim_id": "4d", "answer": "Yes", "1st_line_citation": "US Const. Amend. V", "citation": "Takings Clause, US Const. Amend. V",
     "sub_sort_order": "1", "jurisdictions": ["Federal"]}, {"claim_id": "1a", "answer": "Yes", "1st_line_citation": "US Const. Amend. I",
                                                            "citation": "Freedom of Association, US Const. Amend. I",
                                                            "sub_sort_order": "1", "jurisdictions": ["Federal"]},
    {"claim_id": "7a", "answer": "Yes", "1st_line_citation": "US Const. Amend. XIV", "citation": "US Const. Amend. XIV",
     "sub_sort_order": "2", "jurisdictions": ["Federal"]},
    {"claim_id": "6f", "answer": "Yes", "1st_line_citation": "US Const. Amend. XIV",
     "citation": "Procedural Due Process, US Const. Amend. XIV", "sub_sort_order": "1", "jurisdictions": ["Federal"]},
    {"claim_id": "3c", "answer": "Yes", "1st_line_citation": "US Const. Amend. IV", "citation": "US Const. Amend. IV",
     "sub_sort_order": "1", "jurisdictions": ["Federal"]},
    {"claim_id": "5e", "answer": "Yes", "1st_line_citation": "US Const. Amend. VIII", "citation": "US Const. Amend. VIII",
     "sub_sort_order": "1", "jurisdictions": ["Federal"]},
    {"claim_id": "8b", "answer": "Yes", "1st_line_citation": "US Const. Art. V", "citation": "US Const. Art. V", "sub_sort_order": "1",
     "jurisdictions": ["Federal"]},
    {"claim_id": "2b", "answer": "Yes", "1st_line_citation": "US Const. Amend. II", "citation": "US Const. Amend. II",
     "sub_sort_order": "1", "jurisdictions": ["Federal"]}]

expected_sort_order_constit = ["1a", "2b", "3c", "4d", "5e", "6f", "7a", "8b"]


def test_sort_constit_fed_cali_mix():
    sorted_results = do_sort(source_law="constitutional", entries=input_docs_constit, is_mock_run=False)
    i = 0
    for result in sorted_results:
        assert result['claim_id'] == expected_sort_order_constit[i]
        i += 1


input_docs_with_quality = [{"claim_id": "7g", "answer": "Yes", "1st_line_citation": "13 USC 145",
                            "citation": "13 USC 145(a)", "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                           {"claim_id": "6f", "answer": "Possibly with Additional Fact",
                            "1st_line_citation": "13 USC 103",
                            "citation": "13 USC 103(a)", "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                           {"claim_id": "5e", "answer": "Yes", "1st_line_citation": "12 USC 123",
                            "citation": "12 USC 123(d)", "sub_sort_order": "4", "jurisdictions": ["Federal"]},
                           {"claim_id": "4d", "answer": "Possibly with Additional Fact",
                            "1st_line_citation": "12 USC 123",
                            "citation": "12 USC 123(c)", "sub_sort_order": "3", "jurisdictions": ["Federal"]},
                           {"claim_id": "3c", "answer": "Yes", "1st_line_citation": "12 USC 123",
                            "citation": "12 USC 123(b)", "sub_sort_order": "2", "jurisdictions": ["Federal"]},
                           {"claim_id": "2b", "answer": "Possibly with Additional Fact",
                            "1st_line_citation": "12 USC 123",
                            "citation": "12 USC 123(a)", "sub_sort_order": "1", "jurisdictions": ["Federal"]},
                           {"claim_id": "1a", "answer": "Possibly with Additional Fact",
                            "1st_line_citation": "11 USC 107",
                            "citation": "11 US 107(a)", "sub_sort_order": "1", "jurisdictions": ["Federal"]}
                           ]

expected_sort_order_with_quality = ["7g", "3c", "5e", "2b", "4d", "1a", "6f"]


def test_sort_with_quality():
    sorted_results = do_sort(source_law="statutes", entries=input_docs_with_quality, is_mock_run=False)
    i = 0
    for result in sorted_results:
        assert result['claim_id'] == expected_sort_order_with_quality[i]
        i += 1
