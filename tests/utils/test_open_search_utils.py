from unittest.mock import patch, MagicMock

import pytest

from app.utils.open_search_util import OpenSearch


@pytest.fixture
def mock_boto_client():
    with patch('boto3.client') as mock_client:
        mock_client().get_secret_value.return_value = {'SecretString': '{"username": "test", "password": "test"}'}
        yield mock_client


@pytest.fixture
def opensearch(mock_boto_client):
    return OpenSearch('secret_open_search', 'us-east-1')


class TestOpenSearch:

    def test_opensearch_init(self, opensearch):
        assert opensearch.username == 'test'
        assert opensearch.password == 'test'

    @patch('requests.post', return_value=MagicMock(status_code=200, json=lambda: 'success'))
    def test_opensearch_mget(self, mock_post, opensearch):
        response = opensearch.mget('query')
        assert response == 'success'

    @patch('requests.post', return_value=MagicMock(status_code=400, text='failure'))
    def test_opensearch_mget_failure(self, mock_post, opensearch):
        with pytest.raises(RuntimeError):
            opensearch.mget('query')

    def test_generate_mget_queries(self, opensearch):
        queries = opensearch.generate_mget_queries('alias', ['guid1', 'guid2'])
        expected_queries = [{"_index": "alias", "_id": "guid1", "_source": ["doc_status", "royalty_id"]},
                            {"_index": "alias", "_id": "guid2", "_source": ["doc_status", "royalty_id"]}]
        assert queries == expected_queries

    @patch('requests.post', return_value=MagicMock(status_code=200, json=lambda: 'success'))
    def test_execute_doc_guid_open_search(self, mock_post, opensearch):
        response = opensearch.execute_doc_guid_open_search(['guid1', 'guid2'])
        assert response == 'success'
