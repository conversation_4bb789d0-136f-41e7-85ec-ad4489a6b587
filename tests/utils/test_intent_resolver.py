from unittest.mock import MagicMock, patch

import pytest
from aalp_service.v2.intent_resolver.config import IntentResolverSettings
from aalp_service.v2.intent_resolver.dataclasses import (IntentResolverServiceOutput, AnnotationType, UserInput,
                                                         Annotation)
from pydantic_settings import BaseSettings

from utils.custom_exceptions import MaliciousContentException
from utils.intent_resolver import copy_model_settings, check_intent


class TestIntentResolver:

    @pytest.fixture
    def mock_intent_settings(self):
        mock = MagicMock(spec=IntentResolverSettings)
        mock.conversation_id = "test_conversation_id"
        mock.conversation_entry_id = "test_conversation_entry_id"
        mock.cobalt_settings = MagicMock()
        mock.cobalt_settings.COBALT_SESSION = {}
        mock.auth_token = "test_auth_token"
        mock.tags = ["test_tag"]
        return mock

    @pytest.fixture
    def mock_base_settings(self):
        return MagicMock(spec=BaseSettings)

    def test_copy_model_settings(self, mock_base_settings):
        mock_settings = mock_base_settings
        mock_settings.CUSTOM_HEADERS = {"LLM-Type": "original"}
        mock_settings.model_copy.return_value = MagicMock()
        mock_settings.model_copy.return_value.CUSTOM_HEADERS = {"LLM-Type": "original-appended"}
        mock_settings.tags = ["test_tag"]

        result = copy_model_settings(mock_settings, "-appended")

        assert result.CUSTOM_HEADERS["LLM-Type"] == "original-appended"
        assert id(result) != id(mock_settings)

    @pytest.mark.asyncio
    @patch('utils.intent_resolver.IntentResolverService.evaluate')
    @patch('utils.intent_resolver.create_event_v2')
    @patch('utils.intent_resolver.send_event')
    async def test_check_intent_sufficient(self, mock_send_event, mock_create_event, mock_evaluate,
                                           mock_intent_settings):
        mock_response = IntentResolverServiceOutput(
            user_input=UserInput(message="test", contentTypes=[], fermiJurisdictions=[]),
            annotations=[Annotation(type=AnnotationType.IS_UNDERSPECIFIED, value=False),
                         Annotation(type=AnnotationType.ADVERSARIAL, value=False),
                         Annotation(type=AnnotationType.QUERY_VALIDATION_ERROR, value=False)])
        mock_evaluate.return_value = mock_response

        result = await check_intent("test_profile", "test message", [], [], [], mock_intent_settings, True)

        assert result == mock_response
        mock_create_event.assert_called_once()
        mock_send_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('utils.intent_resolver.IntentResolverService.evaluate')
    @patch('utils.intent_resolver.create_event_v2')
    @patch('utils.intent_resolver.send_event')
    async def test_check_intent_underspecified(self, mock_send_event, mock_create_event, mock_evaluate,
                                               mock_intent_settings):
        mock_response = IntentResolverServiceOutput(
            user_input=UserInput(message="test", contentTypes=[], fermiJurisdictions=[]),
            annotations=[Annotation(type=AnnotationType.IS_UNDERSPECIFIED, value=True)])
        mock_evaluate.return_value = mock_response

        with pytest.raises(MaliciousContentException):
            await check_intent("test_profile", "test message", [], [], [], mock_intent_settings, True)

        mock_create_event.assert_called_once()
        mock_send_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('utils.intent_resolver.IntentResolverService.evaluate')
    @patch('utils.intent_resolver.create_event_v2')
    @patch('utils.intent_resolver.send_event')
    async def test_check_intent_adversarial(self, mock_send_event, mock_create_event, mock_evaluate,
                                            mock_intent_settings):
        mock_response = IntentResolverServiceOutput(
            user_input=UserInput(message="test", contentTypes=[], fermiJurisdictions=[]),
            annotations=[Annotation(type=AnnotationType.ADVERSARIAL, value=True)])
        mock_evaluate.return_value = mock_response

        with pytest.raises(MaliciousContentException):
            await check_intent("test_profile", "test message", [], [], [], mock_intent_settings, True)

        mock_create_event.assert_called_once()
        mock_send_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('utils.intent_resolver.IntentResolverService.evaluate')
    @patch('utils.intent_resolver.create_event_v2')
    @patch('utils.intent_resolver.send_event')
    async def test_check_intent_query_validation_error(self, mock_send_event, mock_create_event, mock_evaluate,
                                                       mock_intent_settings):
        mock_response = IntentResolverServiceOutput(
            user_input=UserInput(message="test", contentTypes=[], fermiJurisdictions=[]),
            annotations=[Annotation(type=AnnotationType.QUERY_VALIDATION_ERROR, value=True)])
        mock_evaluate.return_value = mock_response

        with pytest.raises(MaliciousContentException):
            await check_intent("test_profile", "test message", [], [], [], mock_intent_settings, True)

        mock_create_event.assert_called_once()
        mock_send_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('utils.intent_resolver.IntentResolverService.evaluate')
    @patch('utils.intent_resolver.create_event_v2')
    @patch('utils.intent_resolver.send_event')
    async def test_check_intent_no_raise_exception(self, mock_send_event, mock_create_event, mock_evaluate,
                                                   mock_intent_settings):
        mock_response = IntentResolverServiceOutput(
            user_input=UserInput(message="test", contentTypes=[], fermiJurisdictions=[]),
            annotations=[Annotation(type=AnnotationType.ADVERSARIAL, value=True)])
        mock_evaluate.return_value = mock_response

        result = await check_intent("test_profile", "test message", [], [], [], mock_intent_settings, False)

        assert result == mock_response
        mock_create_event.assert_called_once()
        mock_send_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('utils.intent_resolver.IntentResolverService.evaluate')
    @patch('utils.intent_resolver.create_event_v2')
    @patch('utils.intent_resolver.send_event')
    async def test_check_intent_multiple_annotations(self, mock_send_event, mock_create_event, mock_evaluate,
                                                     mock_intent_settings):
        mock_response = IntentResolverServiceOutput(
            user_input=UserInput(message="test", contentTypes=[], fermiJurisdictions=[]),
            annotations=[Annotation(type=AnnotationType.IS_UNDERSPECIFIED, value=False),
                         Annotation(type=AnnotationType.ADVERSARIAL, value=False),
                         Annotation(type=AnnotationType.QUERY_VALIDATION_ERROR, value=False)])
        mock_evaluate.return_value = mock_response

        result = await check_intent("test_profile", "test message", [], [], [], mock_intent_settings, True)

        assert result == mock_response
        mock_create_event.assert_called_once()
        mock_send_event.assert_called_once()
