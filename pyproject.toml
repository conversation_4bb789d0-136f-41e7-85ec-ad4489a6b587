[tool.poetry]
name = "ras-search-ai-rag-westlaw-claims-explorer"
version = "0.1.0"
description = "AI Westlaw Claims Explorer"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "~3.11"
fastapi = "^0.115.5"
raslogger = "^0.4.0"
python-dotenv = "1.0.0"
moto="4.1.10"
pytest="8.2"
ras-shared-python-conversation-core="^1.17.27"
ras-shared-python-common-utils="^1.17.27"
ras-shared-python-configuration-utils="^1.17.27"
ras-shared-python-events-client="^1.17.27"
ras-shared-python-datadog-utils="^1.17.27"
ras-shared-python-gcs-utils="^1.17.27"
ras-shared-python-llm-proxy-client="^1.17.27"
pandas = "^2.1.4"
duckdb = "^0.9.2"
dataclasses-json = "^0.5.14"
celery = "^5.3.1"
labs-aalp-service = "1.17.7"
labs_pipeline = "1.10.0"
sniffio = "^1.3.1"
apscheduler = "^3.10.4"
psutil = "5.9.5"
azure-identity = "~1.19.0"
pydantic-core = "2.23.4"
pydantic = "2.9.2"
h11 = "^0.16.0"
httpcore = "^1.0.9"
anyio = "^4.6.0"
jiter = "^0.6.0"
tokenizers = "0.21.2"

[tool.poetry.group.dev.dependencies]
poethepoet = "^0.24.4"
pytest="8.2"
pytest-env="1.0.0"
pytest-cov = "^4.1.0"
pytest-html = "^4.1.1"
mock = "^5.1.0"
mockito = "^1.4.0"
pytest-celery = "^0.0.0"
pytest-asyncio = "0.24.0"
black = "23.3.0"
ras-shared-python-ai-conversations-qa-testing="^1.16.33"

[tool.black]
# See other configurations available at https://pypi.org/project/black/
line-length = 120
target-version = ['py311']

[tool.poe.tasks]
pytest = "pytest --cache-clear --junitxml=reports/pytest_junit.xml --html=reports/pytest.html --self-contained-html --cov-report=html:reports/coverage_html --cov-report=xml:reports/coverage.xml"

[[tool.poetry.source]]
name = "tr"
url = "https://tr1.jfrog.io/artifactory/api/pypi/pypi/simple"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
default = true

