[pytest]
# PyTest docs: https://docs.pytest.org/en/latest/contents.html#toc
# PyTest INI docs: https://docs.pytest.org/en/latest/reference.html#ini-options-ref
# Note pycharm/IDEA users https://youtrack.jetbrains.com/issue/PY-40980?_ga=2.115217028.2092377383.1589996928-5481592.1589996928
# Using black: https://pypi.org/project/pytest-black/
# Using flake8-bugbear: https://pypi.org/project/flake8-bugbear/
# Using pytest-html for reporting https://pypi.org/project/pytest-html/
# junit reporting https://docs.pytest.org/en/latest/usage.html?highlight=--junit#creating-junitxml-format-files
minversion = 5.0

pythonpath = app
testpaths = tests

junit_family = xunit2
junit_logging = all

log_auto_indent = True
log_format = %(asctime)s %(levelname)s %(message)s
log_date_format = %Y-%m-%d %H:%M:%S

# export PYTEST_ADDOPTS="-rsxX -q ..."
# NOTE IntelliJ can't debug unit tests with coverage turned on:
#        https://github.com/pytest-dev/pytest-cov/issues/131
#        https://youtrack.jetbrains.com/issue/PY-20186
#addopts =
    #--cov=app
    #--cov-report=term

log_cli = True
log_cli_level = INFO

env =
    ENVIRONMENT=test
    RESOURCES_DIR=./app/config/resources
    TZ=UTC